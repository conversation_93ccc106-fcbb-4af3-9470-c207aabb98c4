{"rustc": 10895048813736897673, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 2101690193974198552, "deps": [[1615478164327904835, "pin_utils", false, 488257045046012475], [1906322745568073236, "pin_project_lite", false, 504774505565702127], [6955678925937229351, "slab", false, 6043759164270119525], [7620660491849607393, "futures_core", false, 1261296945117100219], [10565019901765856648, "futures_macro", false, 4820054508135509467], [16240732885093539806, "futures_task", false, 2352109585759545092]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-dc07bfc54bff6aa3\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}