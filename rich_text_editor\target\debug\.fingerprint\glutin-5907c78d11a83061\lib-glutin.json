{"rustc": 10895048813736897673, "features": "[\"egl\", \"glutin_egl_sys\", \"glutin_glx_sys\", \"glutin_wgl_sys\", \"glx\", \"libloading\", \"wayland\", \"wayland-sys\", \"wgl\", \"windows-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"egl\", \"glutin_egl_sys\", \"glutin_glx_sys\", \"glutin_wgl_sys\", \"glx\", \"libloading\", \"wayland\", \"wayland-sys\", \"wgl\", \"windows-sys\", \"x11\", \"x11-dl\"]", "target": 1600304521922079521, "profile": 2241668132362809309, "path": 9156147889705076316, "deps": [[3722963349756955755, "once_cell", false, 16515743069243878334], [4143744114649553716, "raw_window_handle", false, 164830378543985072], [6763978947554154845, "windows_sys", false, 1516860157156185611], [7191709312698686449, "glutin_egl_sys", false, 6172115373359319228], [7478509218540386589, "glutin_wgl_sys", false, 4356136804640578557], [7896293946984509699, "bitflags", false, 12160561165587510770], [10058659651543567831, "build_script_build", false, 2350269857169477567], [13587469111750606423, "libloading", false, 1649644108591390276]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\glutin-5907c78d11a83061\\dep-lib-glutin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}