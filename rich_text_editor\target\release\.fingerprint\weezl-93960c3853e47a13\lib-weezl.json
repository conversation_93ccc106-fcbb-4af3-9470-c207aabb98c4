{"rustc": 10895048813736897673, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"async\", \"default\", \"futures\", \"std\"]", "target": 8369499057004385739, "profile": 17984201634715228204, "path": 8129429860494916826, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\weezl-93960c3853e47a13\\dep-lib-weezl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}