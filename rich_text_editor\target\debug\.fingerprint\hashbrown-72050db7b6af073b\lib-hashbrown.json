{"rustc": 10895048813736897673, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\", \"rayon\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2225463790103693989, "path": 11035767788352647301, "deps": [[5230392855116717286, "equivalent", false, 1023420005725077846], [9150530836556604396, "allocator_api2", false, 12036480407338680665], [10697383615564341592, "rayon", false, 11051457319962534131], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 2529473569765601961]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-72050db7b6af073b\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}