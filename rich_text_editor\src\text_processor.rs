use unicode_segmentation::UnicodeSegmentation;
use slint::Color;

/// 字符类型枚举
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub enum CharType {
    Chinese,    // 汉字
    English,    // 英文字母
    Number,     // 数字
    Punctuation, // 标点符号
    Whitespace, // 空白字符
    Other,      // 其他字符
}

/// 文本片段结构
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct TextSegmentData {
    pub text: String,
    pub char_type: CharType,
    pub color: Color,
}

/// 文本处理器
#[derive(Clone)]
pub struct TextProcessor;

impl TextProcessor {
    pub fn new() -> Self {
        Self
    }

    /// 处理文本，返回带颜色的文本片段（按行处理）
    pub fn process_text(&self, text: &str) -> Vec<TextSegmentData> {
        let mut segments = Vec::new();

        // 按行分割文本
        for line in text.lines() {
            if line.is_empty() {
                // 空行也要保留
                segments.push(TextSegmentData {
                    text: "\n".to_string(),
                    char_type: CharType::Whitespace,
                    color: self.get_color_for_type(&CharType::Whitespace),
                });
                continue;
            }

            // 处理每行内的字符类型分段
            let line_segments = self.process_line(line);
            segments.extend(line_segments);

            // 添加换行符（除了最后一行）
            segments.push(TextSegmentData {
                text: "\n".to_string(),
                char_type: CharType::Whitespace,
                color: self.get_color_for_type(&CharType::Whitespace),
            });
        }

        // 如果原文本不以换行符结尾，移除最后添加的换行符
        if !text.ends_with('\n') && !segments.is_empty() {
            if let Some(last) = segments.last() {
                if last.text == "\n" {
                    segments.pop();
                }
            }
        }

        segments
    }

    /// 处理单行文本，返回该行的颜色片段
    fn process_line(&self, line: &str) -> Vec<TextSegmentData> {
        let mut segments = Vec::new();
        let mut current_segment = String::new();
        let mut current_type = None;

        // 使用Unicode分割器处理文本
        for grapheme in line.graphemes(true) {
            let char_type = self.classify_character(grapheme);

            // 如果字符类型改变，保存当前片段并开始新片段
            if current_type.as_ref() != Some(&char_type) {
                if !current_segment.is_empty() {
                    if let Some(prev_type) = current_type {
                        segments.push(TextSegmentData {
                            text: current_segment.clone(),
                            char_type: prev_type.clone(),
                            color: self.get_color_for_type(&prev_type),
                        });
                    }
                    current_segment.clear();
                }
                current_type = Some(char_type);
            }

            current_segment.push_str(grapheme);
        }

        // 处理最后一个片段
        if !current_segment.is_empty() {
            if let Some(char_type) = current_type {
                segments.push(TextSegmentData {
                    text: current_segment,
                    char_type: char_type.clone(),
                    color: self.get_color_for_type(&char_type),
                });
            }
        }

        segments
    }

    /// 分类字符类型
    fn classify_character(&self, grapheme: &str) -> CharType {
        let first_char = grapheme.chars().next().unwrap_or('\0');

        match first_char {
            // 汉字范围 (CJK统一汉字)
            '\u{4E00}'..='\u{9FFF}' |
            '\u{3400}'..='\u{4DBF}' |
            '\u{20000}'..='\u{2A6DF}' |
            '\u{2A700}'..='\u{2B73F}' |
            '\u{2B740}'..='\u{2B81F}' |
            '\u{2B820}'..='\u{2CEAF}' => CharType::Chinese,

            // 英文字母
            'a'..='z' | 'A'..='Z' => CharType::English,

            // 数字
            '0'..='9' => CharType::Number,

            // 空白字符
            ' ' | '\t' | '\n' | '\r' => CharType::Whitespace,

            // 常见标点符号
            '!' | '@' | '#' | '$' | '%' | '^' | '&' | '*' | '(' | ')' |
            '-' | '_' | '=' | '+' | '[' | ']' | '{' | '}' | '\\' | '|' |
            ';' | ':' | '\'' | '"' | ',' | '.' | '<' | '>' | '/' | '?' |
            '`' | '~' |
            // 中文标点符号
            '，' | '。' | '！' | '？' | '；' | '：' | '"' | '"' | '\'' | '\'' |
            '（' | '）' | '【' | '】' | '《' | '》' | '、' | '…' | '—' => CharType::Punctuation,

            // 其他字符
            _ => CharType::Other,
        }
    }

    /// 根据字符类型获取颜色
    fn get_color_for_type(&self, char_type: &CharType) -> Color {
        match char_type {
            CharType::Chinese => Color::from_rgb_u8(220, 20, 60),    // 深红色 - 汉字
            CharType::English => Color::from_rgb_u8(30, 144, 255),   // 蓝色 - 英文
            CharType::Number => Color::from_rgb_u8(34, 139, 34),     // 绿色 - 数字
            CharType::Punctuation => Color::from_rgb_u8(148, 0, 211), // 紫色 - 标点符号
            CharType::Whitespace => Color::from_rgb_u8(128, 128, 128), // 灰色 - 空白
            CharType::Other => Color::from_rgb_u8(255, 140, 0),      // 橙色 - 其他
        }
    }

    /// 获取字符类型的描述
    pub fn get_type_description(&self, char_type: &CharType) -> &'static str {
        match char_type {
            CharType::Chinese => "汉字",
            CharType::English => "英文",
            CharType::Number => "数字",
            CharType::Punctuation => "标点",
            CharType::Whitespace => "空白",
            CharType::Other => "其他",
        }
    }

    /// 统计文本中各类型字符的数量
    pub fn count_character_types(&self, text: &str) -> std::collections::HashMap<CharType, usize> {
        let mut counts = std::collections::HashMap::new();

        for grapheme in text.graphemes(true) {
            let char_type = self.classify_character(grapheme);
            *counts.entry(char_type).or_insert(0) += 1;
        }

        counts
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_character_classification() {
        let processor = TextProcessor::new();

        assert_eq!(processor.classify_character("中"), CharType::Chinese);
        assert_eq!(processor.classify_character("A"), CharType::English);
        assert_eq!(processor.classify_character("1"), CharType::Number);
        assert_eq!(processor.classify_character("，"), CharType::Punctuation);
        assert_eq!(processor.classify_character(" "), CharType::Whitespace);
    }

    #[test]
    fn test_text_processing() {
        let processor = TextProcessor::new();
        let segments = processor.process_text("Hello世界123！");

        assert_eq!(segments.len(), 4);
        assert_eq!(segments[0].text, "Hello");
        assert_eq!(segments[0].char_type, CharType::English);
        assert_eq!(segments[1].text, "世界");
        assert_eq!(segments[1].char_type, CharType::Chinese);
        assert_eq!(segments[2].text, "123");
        assert_eq!(segments[2].char_type, CharType::Number);
        assert_eq!(segments[3].text, "！");
        assert_eq!(segments[3].char_type, CharType::Punctuation);
    }
}
