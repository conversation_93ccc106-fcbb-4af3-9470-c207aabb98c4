{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4792644333863610003, "build_script_build", false, 15081032569868108646]], "local": [{"RerunIfChanged": {"output": "release\\build\\rich_text_editor-e168f84cd16e4971\\output", "paths": ["target\\release\\build\\SLINT_DEFAULT_STYLE.txt", "ui/main.slint"]}}, {"RerunIfEnvChanged": {"var": "SLINT_STYLE", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_FONT_SIZES", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_SCALE_FACTOR", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_ASSET_SECTION", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_EMBED_RESOURCES", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_EMIT_DEBUG_INFO", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}