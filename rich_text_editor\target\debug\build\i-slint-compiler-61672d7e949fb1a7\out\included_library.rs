
fn widget_library() -> &'static [(&'static str, &'static BuiltinDirectory<'static>)] {
    &[
("common", &[&BuiltinFile {path: r#"about-slint.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\about-slint.slint"#))},&BuiltinFile {path: r#"combobox-base.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\combobox-base.slint"#))},&BuiltinFile {path: r#"datepicker_base.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\datepicker_base.slint"#))},&BuiltinFile {path: r#"internal-components.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\internal-components.slint"#))},&BuiltinFile {path: r#"layout.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\layout.slint"#))},&BuiltinFile {path: r#"lineedit-base.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\lineedit-base.slint"#))},&BuiltinFile {path: r#"listview.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\listview.slint"#))},&BuiltinFile {path: r#"MadeWithSlint-logo-dark.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\MadeWithSlint-logo-dark.svg"#))},&BuiltinFile {path: r#"MadeWithSlint-logo-light.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\MadeWithSlint-logo-light.svg"#))},&BuiltinFile {path: r#"menu-base.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\menu-base.slint"#))},&BuiltinFile {path: r#"menus.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\menus.slint"#))},&BuiltinFile {path: r#"slider-base.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\slider-base.slint"#))},&BuiltinFile {path: r#"spinbox-base.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\spinbox-base.slint"#))},&BuiltinFile {path: r#"spinner-base.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\spinner-base.slint"#))},&BuiltinFile {path: r#"standardbutton.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\standardbutton.slint"#))},&BuiltinFile {path: r#"tabwidget-base.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\tabwidget-base.slint"#))},&BuiltinFile {path: r#"textedit-base.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\textedit-base.slint"#))},&BuiltinFile {path: r#"time-picker-base.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\common\time-picker-base.slint"#))}]),
("cosmic", &[&BuiltinFile {path: r#"button.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\button.slint"#))},&BuiltinFile {path: r#"checkbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\checkbox.slint"#))},&BuiltinFile {path: r#"color-scheme.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\color-scheme.slint"#))},&BuiltinFile {path: r#"combobox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\combobox.slint"#))},&BuiltinFile {path: r#"components.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\components.slint"#))},&BuiltinFile {path: r#"datepicker.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\datepicker.slint"#))},&BuiltinFile {path: r#"groupbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\groupbox.slint"#))},&BuiltinFile {path: r#"lineedit.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\lineedit.slint"#))},&BuiltinFile {path: r#"menu.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\menu.slint"#))},&BuiltinFile {path: r#"progressindicator.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\progressindicator.slint"#))},&BuiltinFile {path: r#"scrollview.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\scrollview.slint"#))},&BuiltinFile {path: r#"slider.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\slider.slint"#))},&BuiltinFile {path: r#"spinbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\spinbox.slint"#))},&BuiltinFile {path: r#"spinner.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\spinner.slint"#))},&BuiltinFile {path: r#"std-widgets-impl.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\std-widgets-impl.slint"#))},&BuiltinFile {path: r#"std-widgets.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\std-widgets.slint"#))},&BuiltinFile {path: r#"styling.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\styling.slint"#))},&BuiltinFile {path: r#"switch.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\switch.slint"#))},&BuiltinFile {path: r#"tableview.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\tableview.slint"#))},&BuiltinFile {path: r#"tabwidget.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\tabwidget.slint"#))},&BuiltinFile {path: r#"textedit.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\textedit.slint"#))},&BuiltinFile {path: r#"time-picker.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\time-picker.slint"#))},&BuiltinFile {path: r#"_arrow_back.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\_arrow_back.svg"#))},&BuiltinFile {path: r#"_arrow_down.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\_arrow_down.svg"#))},&BuiltinFile {path: r#"_arrow_forward.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\_arrow_forward.svg"#))},&BuiltinFile {path: r#"_arrow_up.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\_arrow_up.svg"#))},&BuiltinFile {path: r#"_calendar.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\_calendar.svg"#))},&BuiltinFile {path: r#"_check-mark.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\_check-mark.svg"#))},&BuiltinFile {path: r#"_clock.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\_clock.svg"#))},&BuiltinFile {path: r#"_edit.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\_edit.svg"#))},&BuiltinFile {path: r#"_keyboard.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\_keyboard.svg"#))},&BuiltinFile {path: r#"_pane_down.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cosmic\_pane_down.svg"#))}]),
("cupertino", &[&BuiltinFile {path: r#"button.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\button.slint"#))},&BuiltinFile {path: r#"checkbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\checkbox.slint"#))},&BuiltinFile {path: r#"color-scheme.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\color-scheme.slint"#))},&BuiltinFile {path: r#"combobox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\combobox.slint"#))},&BuiltinFile {path: r#"components.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\components.slint"#))},&BuiltinFile {path: r#"datepicker.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\datepicker.slint"#))},&BuiltinFile {path: r#"groupbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\groupbox.slint"#))},&BuiltinFile {path: r#"lineedit.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\lineedit.slint"#))},&BuiltinFile {path: r#"menu.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\menu.slint"#))},&BuiltinFile {path: r#"progressindicator.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\progressindicator.slint"#))},&BuiltinFile {path: r#"scrollview.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\scrollview.slint"#))},&BuiltinFile {path: r#"slider.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\slider.slint"#))},&BuiltinFile {path: r#"spinbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\spinbox.slint"#))},&BuiltinFile {path: r#"spinner.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\spinner.slint"#))},&BuiltinFile {path: r#"std-widgets-impl.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\std-widgets-impl.slint"#))},&BuiltinFile {path: r#"std-widgets.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\std-widgets.slint"#))},&BuiltinFile {path: r#"styling.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\styling.slint"#))},&BuiltinFile {path: r#"switch.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\switch.slint"#))},&BuiltinFile {path: r#"tableview.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\tableview.slint"#))},&BuiltinFile {path: r#"tabwidget.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\tabwidget.slint"#))},&BuiltinFile {path: r#"textedit.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\textedit.slint"#))},&BuiltinFile {path: r#"time-picker.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\time-picker.slint"#))},&BuiltinFile {path: r#"_arrow-down.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_arrow-down.svg"#))},&BuiltinFile {path: r#"_arrow-up.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_arrow-up.svg"#))},&BuiltinFile {path: r#"_arrow_back.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_arrow_back.svg"#))},&BuiltinFile {path: r#"_arrow_forward.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_arrow_forward.svg"#))},&BuiltinFile {path: r#"_calendar.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_calendar.svg"#))},&BuiltinFile {path: r#"_check-mark.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_check-mark.svg"#))},&BuiltinFile {path: r#"_chevron-down.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_chevron-down.svg"#))},&BuiltinFile {path: r#"_chevron-up.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_chevron-up.svg"#))},&BuiltinFile {path: r#"_clock.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_clock.svg"#))},&BuiltinFile {path: r#"_down.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_down.svg"#))},&BuiltinFile {path: r#"_dropdown.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_dropdown.svg"#))},&BuiltinFile {path: r#"_edit.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_edit.svg"#))},&BuiltinFile {path: r#"_keyboard.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_keyboard.svg"#))},&BuiltinFile {path: r#"_left.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_left.svg"#))},&BuiltinFile {path: r#"_right.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_right.svg"#))},&BuiltinFile {path: r#"_up.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\cupertino\_up.svg"#))}]),
("fluent", &[&BuiltinFile {path: r#"button.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\button.slint"#))},&BuiltinFile {path: r#"checkbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\checkbox.slint"#))},&BuiltinFile {path: r#"color-scheme.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\color-scheme.slint"#))},&BuiltinFile {path: r#"combobox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\combobox.slint"#))},&BuiltinFile {path: r#"components.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\components.slint"#))},&BuiltinFile {path: r#"datepicker.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\datepicker.slint"#))},&BuiltinFile {path: r#"groupbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\groupbox.slint"#))},&BuiltinFile {path: r#"lineedit.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\lineedit.slint"#))},&BuiltinFile {path: r#"menu.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\menu.slint"#))},&BuiltinFile {path: r#"progressindicator.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\progressindicator.slint"#))},&BuiltinFile {path: r#"scrollview.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\scrollview.slint"#))},&BuiltinFile {path: r#"slider.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\slider.slint"#))},&BuiltinFile {path: r#"spinbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\spinbox.slint"#))},&BuiltinFile {path: r#"spinner.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\spinner.slint"#))},&BuiltinFile {path: r#"std-widgets-impl.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\std-widgets-impl.slint"#))},&BuiltinFile {path: r#"std-widgets.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\std-widgets.slint"#))},&BuiltinFile {path: r#"styling.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\styling.slint"#))},&BuiltinFile {path: r#"switch.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\switch.slint"#))},&BuiltinFile {path: r#"tableview.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\tableview.slint"#))},&BuiltinFile {path: r#"tabwidget.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\tabwidget.slint"#))},&BuiltinFile {path: r#"textedit.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\textedit.slint"#))},&BuiltinFile {path: r#"time-picker.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\time-picker.slint"#))},&BuiltinFile {path: r#"_arrow-down.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_arrow-down.svg"#))},&BuiltinFile {path: r#"_arrow-up.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_arrow-up.svg"#))},&BuiltinFile {path: r#"_arrow_back.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_arrow_back.svg"#))},&BuiltinFile {path: r#"_arrow_forward.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_arrow_forward.svg"#))},&BuiltinFile {path: r#"_calendar.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_calendar.svg"#))},&BuiltinFile {path: r#"_check-mark.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_check-mark.svg"#))},&BuiltinFile {path: r#"_chevron-down.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_chevron-down.svg"#))},&BuiltinFile {path: r#"_chevron-up.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_chevron-up.svg"#))},&BuiltinFile {path: r#"_clock.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_clock.svg"#))},&BuiltinFile {path: r#"_down.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_down.svg"#))},&BuiltinFile {path: r#"_dropdown.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_dropdown.svg"#))},&BuiltinFile {path: r#"_edit.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_edit.svg"#))},&BuiltinFile {path: r#"_keyboard.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_keyboard.svg"#))},&BuiltinFile {path: r#"_left.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_left.svg"#))},&BuiltinFile {path: r#"_right.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_right.svg"#))},&BuiltinFile {path: r#"_up.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\fluent\_up.svg"#))}]),
("material", &[&BuiltinFile {path: r#"button.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\button.slint"#))},&BuiltinFile {path: r#"checkbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\checkbox.slint"#))},&BuiltinFile {path: r#"color-scheme.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\color-scheme.slint"#))},&BuiltinFile {path: r#"combobox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\combobox.slint"#))},&BuiltinFile {path: r#"components.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\components.slint"#))},&BuiltinFile {path: r#"datepicker.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\datepicker.slint"#))},&BuiltinFile {path: r#"groupbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\groupbox.slint"#))},&BuiltinFile {path: r#"lineedit.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\lineedit.slint"#))},&BuiltinFile {path: r#"menu.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\menu.slint"#))},&BuiltinFile {path: r#"progressindicator.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\progressindicator.slint"#))},&BuiltinFile {path: r#"scrollview.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\scrollview.slint"#))},&BuiltinFile {path: r#"slider.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\slider.slint"#))},&BuiltinFile {path: r#"spinbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\spinbox.slint"#))},&BuiltinFile {path: r#"spinner.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\spinner.slint"#))},&BuiltinFile {path: r#"std-widgets-impl.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\std-widgets-impl.slint"#))},&BuiltinFile {path: r#"std-widgets.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\std-widgets.slint"#))},&BuiltinFile {path: r#"styling.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\styling.slint"#))},&BuiltinFile {path: r#"switch.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\switch.slint"#))},&BuiltinFile {path: r#"tableview.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\tableview.slint"#))},&BuiltinFile {path: r#"tabwidget.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\tabwidget.slint"#))},&BuiltinFile {path: r#"textedit.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\textedit.slint"#))},&BuiltinFile {path: r#"time-picker.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\time-picker.slint"#))},&BuiltinFile {path: r#"_arrow-downward.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\_arrow-downward.svg"#))},&BuiltinFile {path: r#"_arrow-drop-down.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\_arrow-drop-down.svg"#))},&BuiltinFile {path: r#"_arrow-drop-up.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\_arrow-drop-up.svg"#))},&BuiltinFile {path: r#"_arrow-upward.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\_arrow-upward.svg"#))},&BuiltinFile {path: r#"_arrow_back.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\_arrow_back.svg"#))},&BuiltinFile {path: r#"_arrow_forward.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\_arrow_forward.svg"#))},&BuiltinFile {path: r#"_calendar.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\_calendar.svg"#))},&BuiltinFile {path: r#"_check-mark.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\_check-mark.svg"#))},&BuiltinFile {path: r#"_clock.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\_clock.svg"#))},&BuiltinFile {path: r#"_edit.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\_edit.svg"#))},&BuiltinFile {path: r#"_expand-more.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\_expand-more.svg"#))},&BuiltinFile {path: r#"_keyboard.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\material\_keyboard.svg"#))}]),
("qt", &[&BuiltinFile {path: r#"button.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\button.slint"#))},&BuiltinFile {path: r#"checkbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\checkbox.slint"#))},&BuiltinFile {path: r#"combobox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\combobox.slint"#))},&BuiltinFile {path: r#"datepicker.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\datepicker.slint"#))},&BuiltinFile {path: r#"groupbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\groupbox.slint"#))},&BuiltinFile {path: r#"internal-scrollview.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\internal-scrollview.slint"#))},&BuiltinFile {path: r#"lineedit.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\lineedit.slint"#))},&BuiltinFile {path: r#"menu.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\menu.slint"#))},&BuiltinFile {path: r#"progressindicator.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\progressindicator.slint"#))},&BuiltinFile {path: r#"scrollview.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\scrollview.slint"#))},&BuiltinFile {path: r#"slider.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\slider.slint"#))},&BuiltinFile {path: r#"spinbox.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\spinbox.slint"#))},&BuiltinFile {path: r#"spinner.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\spinner.slint"#))},&BuiltinFile {path: r#"std-widgets-impl.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\std-widgets-impl.slint"#))},&BuiltinFile {path: r#"std-widgets.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\std-widgets.slint"#))},&BuiltinFile {path: r#"styling.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\styling.slint"#))},&BuiltinFile {path: r#"switch.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\switch.slint"#))},&BuiltinFile {path: r#"tableview.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\tableview.slint"#))},&BuiltinFile {path: r#"tabwidget.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\tabwidget.slint"#))},&BuiltinFile {path: r#"textedit.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\textedit.slint"#))},&BuiltinFile {path: r#"time-picker.slint"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\time-picker.slint"#))},&BuiltinFile {path: r#"_arrow_back.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\_arrow_back.svg"#))},&BuiltinFile {path: r#"_arrow_forward.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\_arrow_forward.svg"#))},&BuiltinFile {path: r#"_calendar.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\_calendar.svg"#))},&BuiltinFile {path: r#"_clock.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\_clock.svg"#))},&BuiltinFile {path: r#"_dropdown.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\_dropdown.svg"#))},&BuiltinFile {path: r#"_edit.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\_edit.svg"#))},&BuiltinFile {path: r#"_keyboard.svg"# , contents: include_bytes!(concat!(env!("CARGO_MANIFEST_DIR"), r#"/widgets\qt\_keyboard.svg"#))}]),
]
}
