{"$message_type":"diagnostic","message":"unreachable pattern","code":{"code":"unreachable_patterns","explanation":null},"level":"warning","spans":[{"file_name":"src\\text_processor.rs","byte_start":4626,"byte_end":4629,"line_start":142,"line_end":142,"column_start":49,"column_end":52,"is_primary":true,"text":[{"text":"            '，' | '。' | '！' | '？' | '；' | '：' | '\"' | '\"' | '\\'' | '\\'' |","highlight_start":49,"highlight_end":52}],"label":"no value can reach this","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\text_processor.rs","byte_start":4466,"byte_end":4469,"line_start":139,"line_end":139,"column_start":32,"column_end":35,"is_primary":false,"text":[{"text":"            ';' | ':' | '\\'' | '\"' | ',' | '.' | '<' | '>' | '/' | '?' |","highlight_start":32,"highlight_end":35}],"label":"matches all the relevant values","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unreachable_patterns)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unreachable pattern\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\text_processor.rs:142:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m139\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ';' | ':' | '\\'' | '\"' | ',' | '.' | '<' | '>' | '/' | '?' |\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmatches all the relevant values\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m142\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            '，' | '。' | '！' | '？' | '；' | '：' | '\"' | '\"' | '\\'' | '\\'' |\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mno value can reach this\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unreachable_patterns)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unreachable pattern","code":{"code":"unreachable_patterns","explanation":null},"level":"warning","spans":[{"file_name":"src\\text_processor.rs","byte_start":4632,"byte_end":4635,"line_start":142,"line_end":142,"column_start":55,"column_end":58,"is_primary":true,"text":[{"text":"            '，' | '。' | '！' | '？' | '；' | '：' | '\"' | '\"' | '\\'' | '\\'' |","highlight_start":55,"highlight_end":58}],"label":"no value can reach this","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\text_processor.rs","byte_start":4466,"byte_end":4469,"line_start":139,"line_end":139,"column_start":32,"column_end":35,"is_primary":false,"text":[{"text":"            ';' | ':' | '\\'' | '\"' | ',' | '.' | '<' | '>' | '/' | '?' |","highlight_start":32,"highlight_end":35}],"label":"matches all the relevant values","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unreachable pattern\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\text_processor.rs:142:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m139\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ';' | ':' | '\\'' | '\"' | ',' | '.' | '<' | '>' | '/' | '?' |\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmatches all the relevant values\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m142\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            '，' | '。' | '！' | '？' | '；' | '：' | '\"' | '\"' | '\\'' | '\\'' |\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mno value can reach this\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unreachable pattern","code":{"code":"unreachable_patterns","explanation":null},"level":"warning","spans":[{"file_name":"src\\text_processor.rs","byte_start":4638,"byte_end":4642,"line_start":142,"line_end":142,"column_start":61,"column_end":65,"is_primary":true,"text":[{"text":"            '，' | '。' | '！' | '？' | '；' | '：' | '\"' | '\"' | '\\'' | '\\'' |","highlight_start":61,"highlight_end":65}],"label":"no value can reach this","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\text_processor.rs","byte_start":4459,"byte_end":4463,"line_start":139,"line_end":139,"column_start":25,"column_end":29,"is_primary":false,"text":[{"text":"            ';' | ':' | '\\'' | '\"' | ',' | '.' | '<' | '>' | '/' | '?' |","highlight_start":25,"highlight_end":29}],"label":"matches all the relevant values","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unreachable pattern\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\text_processor.rs:142:61\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m139\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ';' | ':' | '\\'' | '\"' | ',' | '.' | '<' | '>' | '/' | '?' |\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmatches all the relevant values\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m142\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            '，' | '。' | '！' | '？' | '；' | '：' | '\"' | '\"' | '\\'' | '\\'' |\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mno value can reach this\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unreachable pattern","code":{"code":"unreachable_patterns","explanation":null},"level":"warning","spans":[{"file_name":"src\\text_processor.rs","byte_start":4645,"byte_end":4649,"line_start":142,"line_end":142,"column_start":68,"column_end":72,"is_primary":true,"text":[{"text":"            '，' | '。' | '！' | '？' | '；' | '：' | '\"' | '\"' | '\\'' | '\\'' |","highlight_start":68,"highlight_end":72}],"label":"no value can reach this","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\text_processor.rs","byte_start":4459,"byte_end":4463,"line_start":139,"line_end":139,"column_start":25,"column_end":29,"is_primary":false,"text":[{"text":"            ';' | ':' | '\\'' | '\"' | ',' | '.' | '<' | '>' | '/' | '?' |","highlight_start":25,"highlight_end":29}],"label":"matches all the relevant values","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unreachable pattern\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\text_processor.rs:142:68\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m139\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ';' | ':' | '\\'' | '\"' | ',' | '.' | '<' | '>' | '/' | '?' |\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmatches all the relevant values\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m142\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            '，' | '。' | '！' | '？' | '；' | '：' | '\"' | '\"' | '\\'' | '\\'' |\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mno value can reach this\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `char_type` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\text_processor.rs","byte_start":396,"byte_end":411,"line_start":17,"line_end":17,"column_start":12,"column_end":27,"is_primary":false,"text":[{"text":"pub struct TextSegmentData {","highlight_start":12,"highlight_end":27}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\text_processor.rs","byte_start":444,"byte_end":453,"line_start":19,"line_end":19,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    pub char_type: CharType,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`TextSegmentData` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `char_type` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\text_processor.rs:19:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct TextSegmentData {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub text: String,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub char_type: CharType,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `TextSegmentData` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `get_type_description` and `count_character_types` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\text_processor.rs","byte_start":554,"byte_end":572,"line_start":27,"line_end":27,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"impl TextProcessor {","highlight_start":1,"highlight_end":19}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\text_processor.rs","byte_start":5575,"byte_end":5595,"line_start":163,"line_end":163,"column_start":12,"column_end":32,"is_primary":true,"text":[{"text":"    pub fn get_type_description(&self, char_type: &CharType) -> &'static str {","highlight_start":12,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\text_processor.rs","byte_start":6007,"byte_end":6028,"line_start":175,"line_end":175,"column_start":12,"column_end":33,"is_primary":true,"text":[{"text":"    pub fn count_character_types(&self, text: &str) -> std::collections::HashMap<CharType, usize> {","highlight_start":12,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `get_type_description` and `count_character_types` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\text_processor.rs:163:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl TextProcessor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m163\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_type_description(&self, char_type: &CharType) -> &'static str {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m175\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn count_character_types(&self, text: &str) -> std::collections::HashMap<CharType, usize> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variant `Image` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\clipboard_handler.rs","byte_start":141,"byte_end":157,"line_start":6,"line_end":6,"column_start":10,"column_end":26,"is_primary":false,"text":[{"text":"pub enum ClipboardContent {","highlight_start":10,"highlight_end":26}],"label":"variant in this enum","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":182,"byte_end":187,"line_start":8,"line_end":8,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    Image(ImageData),","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`ClipboardContent` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variant `Image` is never constructed\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\clipboard_handler.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum ClipboardContent {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mvariant in this enum\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Text(String),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Image(ImageData),\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `ClipboardContent` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `data`, `width`, `height`, and `format` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\clipboard_handler.rs","byte_start":261,"byte_end":270,"line_start":13,"line_end":13,"column_start":12,"column_end":21,"is_primary":false,"text":[{"text":"pub struct ImageData {","highlight_start":12,"highlight_end":21}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":281,"byte_end":285,"line_start":14,"line_end":14,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    pub data: Vec<u8>,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":304,"byte_end":309,"line_start":15,"line_end":15,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"    pub width: u32,","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":324,"byte_end":330,"line_start":16,"line_end":16,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    pub height: u32,","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":345,"byte_end":351,"line_start":17,"line_end":17,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    pub format: ImageFormat,","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`ImageData` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `data`, `width`, `height`, and `format` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\clipboard_handler.rs:14:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ImageData {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub data: Vec<u8>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub width: u32,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub height: u32,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub format: ImageFormat,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `ImageData` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variants `Rgba8`, `Rgb8`, `Png`, `Jpeg`, and `Bmp` are never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\clipboard_handler.rs","byte_start":428,"byte_end":439,"line_start":22,"line_end":22,"column_start":10,"column_end":21,"is_primary":false,"text":[{"text":"pub enum ImageFormat {","highlight_start":10,"highlight_end":21}],"label":"variants in this enum","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":446,"byte_end":451,"line_start":23,"line_end":23,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    Rgba8,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":457,"byte_end":461,"line_start":24,"line_end":24,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    Rgb8,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":467,"byte_end":470,"line_start":25,"line_end":25,"column_start":5,"column_end":8,"is_primary":true,"text":[{"text":"    Png,","highlight_start":5,"highlight_end":8}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":476,"byte_end":480,"line_start":26,"line_end":26,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    Jpeg,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":486,"byte_end":489,"line_start":27,"line_end":27,"column_start":5,"column_end":8,"is_primary":true,"text":[{"text":"    Bmp,","highlight_start":5,"highlight_end":8}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`ImageFormat` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variants `Rgba8`, `Rgb8`, `Png`, `Jpeg`, and `Bmp` are never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\clipboard_handler.rs:23:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum ImageFormat {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mvariants in this enum\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Rgba8,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Rgb8,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Png,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Jpeg,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Bmp,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `ImageFormat` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `get_clipboard_image`, `set_clipboard_text`, `has_text`, `has_image`, and `get_clipboard_info` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\clipboard_handler.rs","byte_start":564,"byte_end":585,"line_start":34,"line_end":34,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"impl ClipboardHandler {","highlight_start":1,"highlight_end":22}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":1324,"byte_end":1343,"line_start":58,"line_end":58,"column_start":12,"column_end":31,"is_primary":true,"text":[{"text":"    pub fn get_clipboard_image(&self) -> Result<ImageData> {","highlight_start":12,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":1471,"byte_end":1489,"line_start":63,"line_end":63,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn set_clipboard_text(&self, text: &str) -> Result<()> {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":1767,"byte_end":1775,"line_start":71,"line_end":71,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"    pub fn has_text(&self) -> bool {","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":1932,"byte_end":1941,"line_start":76,"line_end":76,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"    pub fn has_image(&self) -> bool {","highlight_start":12,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\clipboard_handler.rs","byte_start":2097,"byte_end":2115,"line_start":81,"line_end":81,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn get_clipboard_info(&self) -> String {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `get_clipboard_image`, `set_clipboard_text`, `has_text`, `has_image`, and `get_clipboard_info` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\clipboard_handler.rs:58:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ClipboardHandler {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m58\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_clipboard_image(&self) -> Result<ImageData> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn set_clipboard_text(&self, text: &str) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m71\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn has_text(&self) -> bool {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m76\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn has_image(&self) -> bool {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m81\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_clipboard_info(&self) -> String {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"10 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 10 warnings emitted\u001b[0m\n\n"}
