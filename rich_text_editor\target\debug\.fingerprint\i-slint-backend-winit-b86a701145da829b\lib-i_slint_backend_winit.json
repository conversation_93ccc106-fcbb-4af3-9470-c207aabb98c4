{"rustc": 10895048813736897673, "features": "[\"accessibility\", \"default\", \"muda\", \"renderer-femtovg\", \"renderer-software\", \"wayland\", \"x11\"]", "declared_features": "[\"accessibility\", \"default\", \"i-slint-renderer-skia\", \"muda\", \"raw-window-handle-06\", \"renderer-femtovg\", \"renderer-skia\", \"renderer-skia-opengl\", \"renderer-skia-vulkan\", \"renderer-software\", \"wayland\", \"x11\"]", "target": 4810267537760664992, "profile": 15657897354478470176, "path": 6905004093955629648, "deps": [[17713427410418917, "vtable", false, 9974195958633572689], [376837177317575824, "softbuffer", false, 64846386376186560], [781484216458329418, "rgb", false, 17110054350659437125], [1580386997979624515, "scoped_tls_hkt", false, 18124594455548730265], [1961690427714978873, "glutin_winit", false, 8014503548176192000], [3666421787376679933, "accesskit", false, 12905838197646047005], [4143744114649553716, "raw_window_handle", false, 17457856937867999881], [4341921533227644514, "muda", false, 6124227551783673136], [5719588853395619134, "copypasta", false, 3633066741970830747], [5756138707274668043, "i_slint_core_macros", false, 5855601678222436503], [5877456377495598509, "winit", false, 5634563075860214868], [7611461696084798871, "lyon_path", false, 12680015474671871100], [7613193051436156431, "i_slint_core", false, 13642983357584844417], [9579903222759891839, "i_slint_renderer_femtovg", false, 8276702509761109272], [10058659651543567831, "glutin", false, 3036539930793469927], [10411997081178400487, "cfg_if", false, 9886716789947666174], [10972790325417729625, "accesskit_winit", false, 7211100994637169296], [11293676373856528358, "derive_more", false, 3525768815292970770], [13690040694845223964, "pin_weak", false, 9298427259631943072], [14074610438553418890, "bytemuck", false, 3160270663818109516], [14974289545650296072, "imgref", false, 2270241907067475394], [15358414700195712381, "scopeguard", false, 11149399461824000965], [16415649488169917901, "i_slint_common", false, 12116325609225140704], [17699788962609858224, "build_script_build", false, 17877164222311523691]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\i-slint-backend-winit-b86a701145da829b\\dep-lib-i_slint_backend_winit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}