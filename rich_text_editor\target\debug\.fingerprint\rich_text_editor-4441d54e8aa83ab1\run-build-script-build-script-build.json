{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4792644333863610003, "build_script_build", false, 11859439587891877058]], "local": [{"RerunIfChanged": {"output": "debug\\build\\rich_text_editor-4441d54e8aa83ab1\\output", "paths": ["target\\debug\\build\\SLINT_DEFAULT_STYLE.txt", "ui/main.slint"]}}, {"RerunIfEnvChanged": {"var": "SLINT_STYLE", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_FONT_SIZES", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_SCALE_FACTOR", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_ASSET_SECTION", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_EMBED_RESOURCES", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_EMIT_DEBUG_INFO", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}