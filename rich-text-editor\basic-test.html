<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>基础测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        
        .chinese { color: red; font-weight: bold; }
        .english { color: blue; }
        .number { color: green; font-weight: bold; }
        .punctuation { color: purple; font-weight: bold; }
        
        .test-area {
            border: 2px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
            font-size: 18px;
        }
        
        button {
            padding: 10px 20px;
            font-size: 16px;
            margin: 5px;
            cursor: pointer;
        }
        
        textarea {
            width: 100%;
            height: 100px;
            font-size: 16px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>富文本颜色测试 - 基础版</h1>
    
    <p>首先测试静态颜色显示：</p>
    <div class="test-area">
        <span class="chinese">这是红色汉字</span>
        <span class="english">This is blue English</span>
        <span class="number">123456</span>
        <span class="punctuation">！？，。</span>
    </div>
    
    <p>如果上面的文字显示了不同颜色，说明CSS工作正常。</p>
    
    <hr>
    
    <p>动态测试：</p>
    <textarea id="input" placeholder="请输入文字">Hello世界123！</textarea>
    
    <div>
        <button onclick="testBasic()">基础测试</button>
        <button onclick="testColors()">颜色测试</button>
        <button onclick="testAlert()">弹窗测试</button>
    </div>
    
    <div id="output" class="test-area">
        点击按钮查看效果...
    </div>
    
    <div id="debug" style="background: #eee; padding: 10px; margin: 10px 0;">
        调试信息...
    </div>

    <script>
        // 测试JavaScript是否工作
        console.log('JavaScript已加载');
        
        function testAlert() {
            alert('JavaScript工作正常！');
        }
        
        function testBasic() {
            document.getElementById('output').innerHTML = '<strong style="color: red;">基础测试成功！</strong>';
            document.getElementById('debug').innerHTML = '基础测试：HTML更新成功';
        }
        
        function testColors() {
            var input = document.getElementById('input');
            var text = input.value;
            
            if (!text) {
                text = 'Hello世界123！';
                input.value = text;
            }
            
            var result = '';
            
            for (var i = 0; i < text.length; i++) {
                var char = text[i];
                var code = char.charCodeAt(0);
                var className = '';
                
                // 简单分类
                if (code >= 0x4E00 && code <= 0x9FFF) {
                    className = 'chinese';
                } else if ((code >= 65 && code <= 90) || (code >= 97 && code <= 122)) {
                    className = 'english';
                } else if (code >= 48 && code <= 57) {
                    className = 'number';
                } else if ('!@#$%^&*()_+-=[]{}|;:\'",.<>/?`~，。！？；：""''（）【】《》、…—'.indexOf(char) !== -1) {
                    className = 'punctuation';
                } else {
                    className = 'other';
                }
                
                if (char === ' ') {
                    result += '&nbsp;';
                } else {
                    result += '<span class="' + className + '">' + char + '</span>';
                }
            }
            
            document.getElementById('output').innerHTML = result;
            document.getElementById('debug').innerHTML = '处理了 ' + text.length + ' 个字符，生成HTML长度: ' + result.length;
        }
        
        // 页面加载完成后执行
        window.onload = function() {
            console.log('页面加载完成');
            document.getElementById('debug').innerHTML = '页面已加载，JavaScript正常工作';
        };
    </script>
</body>
</html>
