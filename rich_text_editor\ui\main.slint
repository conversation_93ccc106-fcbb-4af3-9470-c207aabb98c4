import { <PERSON>ton, VerticalBox, HorizontalBox, ScrollView, TextEdit } from "std-widgets.slint";

// 文本片段结构，用于富文本显示
export struct TextSegment {
    text: string,
    color: color,
    font-size: length,
}

// 富文本显示组件
component RichTextDisplay inherits Rectangle {
    in property <[TextSegment]> text-segments;

    background: white;
    border-width: 1px;
    border-color: #ddd;

    ScrollView {
        viewport-width: parent.width;
        viewport-height: parent.height;

        Rectangle {
            width: max(parent.width, content-width);
            height: max(parent.height, content-height);

            property <length> content-width: parent.width;
            property <length> content-height: max(parent.height, text-segments.length * 20px + 100px);

            // 彩色文本显示
            for segment[i] in text-segments: Text {
                text: segment.text;
                color: segment.color;
                font-size: segment.font-size;
                x: 10px;
                y: 10px + i * 20px;
                width: parent.width - 20px;
                height: 20px;
                wrap: word-wrap;
            }
        }
    }
}

// 文本输入组件
component TextInputArea inherits Rectangle {
    in-out property <string> text: "";
    callback text-changed(string);

    background: rgba(255, 255, 255, 0.8); // 半透明背景
    border-width: 1px;
    border-color: #ddd;

    TextEdit {
        width: parent.width - 20px;
        height: parent.height - 20px;
        x: 10px;
        y: 10px;
        text <=> root.text;
        font-size: 14px;
        wrap: word-wrap;
        edited(text) => {
            root.text-changed(text);
        }
    }
}

// 主窗口
export component MainWindow inherits Window {
    title: "富文本记事本 - Rust + Slint";
    width: 800px;
    height: 600px;

    // 回调函数
    callback text-changed(string);
    callback paste-content();
    callback new-file();

    // 属性
    in-out property <string> current-text: "";
    in-out property <[TextSegment]> text-segments: [];
    in-out property <string> status-text: "就绪 - 自动保存已启用";

    VerticalBox {
        spacing: 0px;

        // 紧凑的菜单栏
        Rectangle {
            height: 30px;
            background: #f8f8f8;
            border-width: 1px;
            border-color: #ddd;

            HorizontalBox {
                padding: 4px;
                spacing: 8px;
                alignment: start;

                Button {
                    text: "New";
                    height: 22px;
                    width: 50px;
                    clicked => {
                        new-file();
                    }
                }

                Button {
                    text: "Paste";
                    height: 22px;
                    width: 50px;
                    clicked => {
                        paste-content();
                    }
                }

                Rectangle { width: 10px; } // 分隔符

                Text {
                    text: "汉字红色 | English蓝色 | 123绿色 | 标点紫色";
                    color: #666;
                    font-size: 11px;
                    vertical-alignment: center;
                }
            }
        }

        // 主编辑区域 - 分屏显示
        HorizontalBox {
            spacing: 2px;

            // 左侧：文本输入区域
            TextInputArea {
                width: parent.width / 2;
                text <=> current-text;
                text-changed(text) => {
                    root.text-changed(text);
                }
            }

            // 右侧：富文本显示区域
            RichTextDisplay {
                width: parent.width / 2;
                text-segments: text-segments;
            }
        }

        // 紧凑的状态栏
        Rectangle {
            height: 18px;
            background: #f0f0f0;
            border-width: 1px;
            border-color: #ddd;

            Text {
                text: status-text;
                color: #666;
                font-size: 10px;
                vertical-alignment: center;
                horizontal-alignment: left;
                x: 8px;
            }
        }
    }
}
