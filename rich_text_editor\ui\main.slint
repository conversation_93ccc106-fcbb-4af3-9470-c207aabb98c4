import { Button, VerticalBox, HorizontalBox, Scroll<PERSON>iew, LineEdit } from "std-widgets.slint";

// 文本片段结构，用于富文本显示
export struct TextSegment {
    text: string,
    color: color,
    font-size: length,
}

// 图片结构
export struct ImageData {
    data: image,
    width: length,
    height: length,
    x: length,
    y: length,
}

// 富文本编辑器组件
component RichTextDisplay inherits Rectangle {
    in property <[TextSegment]> text-segments;
    in property <[ImageData]> images;

    background: white;

    ScrollView {
        viewport-width: parent.width;
        viewport-height: parent.height;

        Rectangle {
            width: max(parent.width, content-width);
            height: max(parent.height, content-height);

            property <length> content-width: 800px;
            property <length> content-height: 600px;

            // 渲染文本片段
            for segment[i] in text-segments: Text {
                text: segment.text;
                color: segment.color;
                font-size: segment.font-size;
                x: mod(i * 20px, parent.width - 200px);
                y: floor(i * 20px / (parent.width - 200px)) * 25px;
                width: 200px;
                height: 25px;
            }

            // 渲染图片
            for img[i] in images: Image {
                source: img.data;
                width: img.width;
                height: img.height;
                x: img.x;
                y: img.y;
            }
        }
    }
}

// 主窗口
export component MainWindow inherits Window {
    title: "富文本编辑器 - Rust + Slint";
    width: 1000px;
    height: 700px;

    // 回调函数
    callback file-new();
    callback file-open();
    callback file-save();
    callback text-input(string);
    callback paste-content();
    callback key-pressed(KeyEvent);

    // 属性
    in-out property <string> current-text: "";
    in-out property <[TextSegment]> text-segments: [];
    in-out property <[ImageData]> images: [];
    in-out property <string> status-text: "就绪";

    VerticalBox {
        // 菜单栏
        HorizontalBox {
            height: 40px;
            padding: 5px;
            spacing: 10px;

            Button {
                text: "新建";
                clicked => { file-new(); }
            }

            Button {
                text: "打开";
                clicked => { file-open(); }
            }

            Button {
                text: "保存";
                clicked => { file-save(); }
            }

            Rectangle { width: 20px; } // 分隔符

            Button {
                text: "粘贴";
                clicked => { paste-content(); }
            }

            Rectangle { } // 填充空间

            Text {
                text: "支持汉字、English、123、标点符号不同颜色显示";
                color: #666;
                vertical-alignment: center;
            }
        }

        // 主编辑区域
        HorizontalBox {
            // 文本输入区域
            VerticalBox {
                width: 300px;
                padding: 10px;

                Text {
                    text: "文本输入区域:";
                    font-weight: 700;
                    height: 25px;
                }

                LineEdit {
                    placeholder-text: "在此输入文本...";
                    text <=> current-text;
                    edited(text) => { text-input(text); }
                }

                Rectangle {
                    height: 10px;
                }

                Text {
                    text: "说明：";
                    font-weight: 700;
                    height: 25px;
                }

                Text {
                    text: "• 汉字显示为红色\n• English显示为蓝色\n• 123数字显示为绿色\n• 标点符号显示为紫色\n• 支持粘贴图片显示";
                    color: #666;
                    wrap: word-wrap;
                }
            }

            // 富文本显示区域
            RichTextDisplay {
                text-segments: text-segments;
                images: images;
                border-width: 1px;
                border-color: #ccc;
            }
        }

        // 状态栏
        Rectangle {
            height: 25px;
            background: #f0f0f0;
            border-width: 1px;
            border-color: #ccc;

            Text {
                text: status-text;
                color: #333;
                vertical-alignment: center;
                horizontal-alignment: left;
                x: 10px;
            }
        }
    }


}
