import { Button, VerticalBox, <PERSON>tal<PERSON><PERSON>, <PERSON><PERSON><PERSON>iew, TextEdit } from "std-widgets.slint";

// 文本片段结构，用于富文本显示
export struct TextSegment {
    text: string,
    color: color,
    font-size: length,
}



// 主窗口
export component MainWindow inherits Window {
    title: "富文本记事本 - Rust + Slint";
    width: 800px;
    height: 600px;

    // 回调函数
    callback text-changed(string);
    callback paste-content();
    callback new-file();

    // 属性
    in-out property <string> current-text: "";
    in-out property <[TextSegment]> text-segments: [];
    in-out property <string> status-text: "就绪 - 自动保存已启用";

    VerticalBox {
        spacing: 0px;

        // 紧凑的菜单栏
        Rectangle {
            height: 30px;
            background: #f8f8f8;
            border-width: 1px;
            border-color: #ddd;

            HorizontalBox {
                padding: 4px;
                spacing: 8px;
                alignment: start;

                Button {
                    text: "New";
                    height: 22px;
                    width: 50px;
                    clicked => {
                        new-file();
                    }
                }

                But<PERSON> {
                    text: "Paste";
                    height: 22px;
                    width: 50px;
                    clicked => {
                        paste-content();
                    }
                }

                Rectangle { width: 10px; } // 分隔符

                Text {
                    text: "汉字红色 | English蓝色 | 123绿色 | 标点紫色";
                    color: #666;
                    font-size: 11px;
                    vertical-alignment: center;
                }
            }
        }

        // 主编辑区域 - 上下分屏（输入框 + 颜色预览）
        VerticalBox {
            spacing: 2px;

            // 上半部分：普通文本编辑器
            Rectangle {
                height: 350px;
                background: white;
                border-width: 1px;
                border-color: #ddd;

                TextEdit {
                    width: parent.width - 20px;
                    height: parent.height - 20px;
                    x: 10px;
                    y: 10px;
                    text <=> current-text;
                    font-size: 14px;
                    wrap: word-wrap;
                    placeholder-text: "在此输入文本，下方会显示颜色预览...";
                    edited(text) => {
                        root.text-changed(text);
                    }
                }

                // 标签
                Text {
                    text: "📝 输入区域";
                    x: 5px;
                    y: -18px;
                    font-size: 11px;
                    color: #666;
                }
            }

            // 下半部分：颜色预览
            Rectangle {
                height: 200px;
                background: #f9f9f9;
                border-width: 1px;
                border-color: #ddd;

                ScrollView {
                    viewport-width: parent.width;
                    viewport-height: parent.height;

                    Rectangle {
                        width: max(parent.width, content-width);
                        height: max(parent.height, content-height);

                        property <length> content-width: parent.width;
                        property <length> content-height: max(parent.height, text-segments.length * 20px + 100px);

                        // 彩色文本预览
                        for segment[i] in text-segments: Text {
                            text: segment.text;
                            color: segment.color;
                            font-size: segment.font-size;
                            x: 10px;
                            y: 10px + i * 20px;
                            width: parent.width - 20px;
                            height: 20px;
                            wrap: word-wrap;
                        }

                        // 如果没有内容，显示提示
                        if text-segments.length == 0: Text {
                            text: "🌈 颜色预览区域\n\n在上方输入文本，这里会显示彩色效果\n\n汉字红色 | English蓝色 | 123绿色 | 标点紫色";
                            color: #999;
                            font-size: 12px;
                            x: 10px;
                            y: 30px;
                            width: parent.width - 20px;
                            wrap: word-wrap;
                        }
                    }
                }

                // 标签
                Text {
                    text: "🌈 颜色预览";
                    x: 5px;
                    y: -18px;
                    font-size: 11px;
                    color: #666;
                }
            }
        }

        // 紧凑的状态栏
        Rectangle {
            height: 18px;
            background: #f0f0f0;
            border-width: 1px;
            border-color: #ddd;

            Text {
                text: status-text;
                color: #666;
                font-size: 10px;
                vertical-alignment: center;
                horizontal-alignment: left;
                x: 8px;
            }
        }
    }
}
