{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"dashmap\", \"enable\", \"once_cell\", \"print_at_exit\", \"rustc-hash\"]", "target": 17911193859055261422, "profile": 2225463790103693989, "path": 3270972939577054315, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\countme-80fcd5e9415cb72d\\dep-lib-countme", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}