{"rustc": 10895048813736897673, "features": "[\"proc-macro-crate\", \"std\"]", "declared_features": "[\"complex-expressions\", \"default\", \"external_doc\", \"proc-macro-crate\", \"std\"]", "target": 600225532800476200, "profile": 2225463790103693989, "path": 14556173194202368792, "deps": [[3060637413840920116, "proc_macro2", false, 12148754458356503610], [15203748914246919255, "proc_macro_crate", false, 12506206538727675362], [17990358020177143287, "quote", false, 6660427873528748432], [18149961000318489080, "syn", false, 15434183285613015808]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\num_enum_derive-59b6e40b909c9a80\\dep-lib-num_enum_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}