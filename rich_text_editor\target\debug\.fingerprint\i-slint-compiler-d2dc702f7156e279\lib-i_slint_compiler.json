{"rustc": 10895048813736897673, "features": "[\"bundle-translations\", \"codemap\", \"codemap-diagnostic\", \"default\", \"display-diagnostics\", \"fontdue\", \"image\", \"proc-macro2\", \"proc_macro_span\", \"quote\", \"rust\", \"software-renderer\"]", "declared_features": "[\"bundle-translations\", \"codemap\", \"codemap-diagnostic\", \"cpp\", \"default\", \"display-diagnostics\", \"fontdue\", \"image\", \"proc-macro2\", \"proc_macro_span\", \"quote\", \"rust\", \"sdf-fonts\", \"software-renderer\"]", "target": 5997077308893045496, "profile": 2225463790103693989, "path": 6121382189225255411, "deps": [[164398694462155020, "typed_index_collections", false, 1792420751846341347], [925920622245451495, "strum", false, 3902658066785783170], [1470679118034951355, "num_enum", false, 7223263368188275948], [1504670837852841510, "rowan", false, 2957169165140076174], [3046214465055776457, "fontdue", false, 2084577200966415000], [3060637413840920116, "proc_macro2", false, 12148754458356503610], [3150220818285335163, "url", false, 301212310175201752], [4264449604758607596, "resvg", false, 3832503368558506778], [5560038387766815647, "codemap", false, 18240095661529961352], [7379558455692600769, "by_address", false, 16451893457741589650], [7611461696084798871, "lyon_path", false, 17014031025793515798], [7721950305594023387, "codemap_diagnostic", false, 14983750712888464686], [7864482126583800221, "linked_hash_set", false, 490649219136958678], [8317047369197848296, "polib", false, 2804058986728961030], [9129745186230991347, "smol_str", false, 8085944834967134405], [10697383615564341592, "rayon", false, 11051457319962534131], [11293676373856528358, "derive_more", false, 16577986434672697943], [13011651213356413521, "lyon_extra", false, 7085858480450903801], [13028763805764736075, "image", false, 18064090798732754140], [14533926308594949856, "build_script_build", false, 10476887528189055058], [16326338539882746041, "itertools", false, 18365963528207662501], [16415649488169917901, "i_slint_common", false, 18305532518666917294], [17990358020177143287, "quote", false, 6660427873528748432]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\i-slint-compiler-d2dc702f7156e279\\dep-lib-i_slint_compiler", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}