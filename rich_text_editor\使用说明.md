# 富文本记事本使用说明

## 🎯 快速开始

### 运行程序
1. 双击 `rich_text_editor.exe` 启动程序
2. 程序会自动加载上次保存的内容（如果有的话）

### 基本操作
- **输入文本**：直接在主编辑区域输入文字
- **换行**：按Enter键换行
- **粘贴**：点击"粘贴"按钮或使用Ctrl+V
- **新建**：点击"新建"按钮清空内容

## 🌈 颜色显示规则

程序会自动为不同类型的字符显示不同颜色：

| 字符类型 | 颜色 | 示例 |
|---------|------|------|
| 汉字 | 🔴 红色 | 中文、汉字、你好 |
| 英文字母 | 🔵 蓝色 | Hello, World, ABC |
| 数字 | 🟢 绿色 | 123, 456, 2025 |
| 标点符号 | 🟣 紫色 | ！？，。；： |
| 空白字符 | ⚪ 灰色 | 空格、制表符 |
| 其他字符 | 🟠 橙色 | 特殊符号等 |

## 💾 自动保存功能

### 自动保存机制
- 程序每3秒自动保存一次当前内容
- 保存文件：`auto_save.txt`（与程序在同一目录）
- 无需手动保存，永不丢失数据

### 数据恢复
- 程序启动时自动加载上次的内容
- 即使程序意外关闭，内容也会保留
- 状态栏会显示"已加载自动保存内容"

## 🔧 界面说明

### 菜单栏（顶部）
- **新建**：清空当前内容，开始新文档
- **粘贴**：粘贴剪贴板中的文本内容
- **提示文字**：显示颜色规则说明

### 主编辑区域（中间）
- 全屏文本编辑器
- 支持多行文本编辑
- 实时颜色显示
- 支持滚动查看长文本

### 状态栏（底部）
- 显示当前字符数
- 显示自动保存状态
- 显示操作提示信息

## 📝 使用技巧

### 测试颜色效果
尝试输入以下文本来查看颜色效果：
```
Hello世界123！这是一个测试文本。
English中文数字456，标点符号：；""''
2025年4月，Rust+Slint开发的富文本编辑器。
```

### 粘贴功能
- 从其他程序复制文本后，点击"粘贴"按钮
- 粘贴的内容会追加到当前文本末尾
- 支持粘贴多行文本

### 数据管理
- 程序会自动保存，无需担心数据丢失
- 如需清空内容，点击"新建"按钮
- 自动保存文件可以手动备份

## ⚠️ 注意事项

1. **文件位置**：自动保存文件在程序同目录下
2. **字符编码**：支持UTF-8编码，完美支持中文
3. **性能**：适合编辑中等长度文本（建议10万字符以内）
4. **兼容性**：仅支持Windows 10/11系统

## 🚀 快捷键

目前支持的快捷键：
- `Ctrl + V`：粘贴（部分支持）

## 📞 问题反馈

如果遇到问题或有改进建议，欢迎反馈！

---

**富文本记事本 v0.1.0**  
*基于 Rust + Slint 开发*
