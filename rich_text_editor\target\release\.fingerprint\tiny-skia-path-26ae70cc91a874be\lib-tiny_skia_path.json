{"rustc": 10895048813736897673, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"libm\", \"no-std-float\", \"std\"]", "target": 15590299274629163093, "profile": 17984201634715228204, "path": 787990138649071216, "deps": [[6060572338472977546, "strict_num", false, 2146886211870735650], [9529943735784919782, "arrayref", false, 13126565928662885397], [14074610438553418890, "bytemuck", false, 14857060863748685789]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tiny-skia-path-26ae70cc91a874be\\dep-lib-tiny_skia_path", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}