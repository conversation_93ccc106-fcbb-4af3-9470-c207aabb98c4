{"rustc": 10895048813736897673, "features": "[\"default\", \"image\", \"image-loading\"]", "declared_features": "[\"debug_inspector\", \"default\", \"glutin\", \"image\", \"image-loading\", \"serde\", \"wgpu\"]", "target": 17446628798878094601, "profile": 144525733998112618, "path": 819262330510429818, "deps": [[781484216458329418, "rgb", false, 10399030925870505790], [1232198224951696867, "unicode_segmentation", false, 5591881404339911524], [1345404220202658316, "fnv", false, 7355686303214738510], [1479280278558536779, "lru", false, 17996446891870310056], [5986029879202738730, "log", false, 393573925909026484], [6997951260445117383, "slotmap", false, 9646000780771812394], [7896293946984509699, "bitflags", false, 13148998852031461988], [9201289626894528338, "glow", false, 11793534334054979817], [12948654253482788520, "unicode_bidi", false, 16025977783428051231], [13028763805764736075, "image", false, 13010041783863854760], [14074610438553418890, "bytemuck", false, 9824448031292301770], [14974289545650296072, "imgref", false, 13316690750361089935], [16960864593395472219, "rustybuzz", false, 17299325019714064362]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\femtovg-5ec54d42f14eb167\\dep-lib-femtovg", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}