{"rustc": 10895048813736897673, "features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"fastrand\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 144525733998112618, "path": 13984392342850178464, "deps": [[376837177317575824, "build_script_build", false, 10807211529182101858], [4143744114649553716, "raw_window_handle", false, 11296632524748108325], [5986029879202738730, "log", false, 393573925909026484], [10281541584571964250, "windows_sys", false, 5447410602740044410]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\softbuffer-7910cd35ad03d86c\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}