{"rustc": 10895048813736897673, "features": "[\"threading\"]", "declared_features": "[\"aom-sys\", \"asm\", \"av-metrics\", \"backtrace\", \"bench\", \"binaries\", \"byteorder\", \"capi\", \"cc\", \"channel-api\", \"check_asm\", \"clap\", \"clap_complete\", \"console\", \"crossbeam\", \"dav1d-sys\", \"decode_test\", \"decode_test_dav1d\", \"default\", \"desync_finder\", \"dump_ivf\", \"dump_lookahead_data\", \"fern\", \"git_version\", \"image\", \"ivf\", \"nasm-rs\", \"nom\", \"quick_test\", \"scan_fmt\", \"scenechange\", \"serde\", \"serde-big-array\", \"serialize\", \"signal-hook\", \"signal_support\", \"threading\", \"toml\", \"tracing\", \"tracing-chrome\", \"tracing-subscriber\", \"unstable\", \"wasm\", \"wasm-bindgen\", \"y4m\"]", "target": 12405811532001061035, "profile": 17984201634715228204, "path": 721753106137371345, "deps": [[1895578031732481377, "profiling", false, 9806326730217083431], [2687729594444538932, "debug_unreachable", false, 1245754549844296202], [2924422107542798392, "libc", false, 3657274068758138752], [3722963349756955755, "once_cell", false, 6788098827412591790], [5157631553186200874, "num_traits", false, 3247025634794560779], [5237962722597217121, "simd_helpers", false, 17550562955543810746], [5626665093607998638, "build_script_build", false, 12053774594334612709], [5986029879202738730, "log", false, 6155596972104726174], [7074416887430417773, "av1_grain", false, 11588860739342110695], [8008191657135824715, "thiserror", false, 3547498965286926248], [10411997081178400487, "cfg_if", false, 12398871947869950992], [11063920846464372013, "v_frame", false, 17210348396593289742], [11263754829263059703, "num_derive", false, 774453928459150739], [12672448913558545127, "noop_proc_macro", false, 13071572239434452889], [13847662864258534762, "arrayvec", false, 633736792643883911], [14931062873021150766, "itertools", false, 11585665843346309650], [16507960196461048755, "rayon", false, 7401960601164134425], [17605717126308396068, "paste", false, 17145178039512193973], [17706129463675219700, "arg_enum_proc_macro", false, 15226412350445631276], [17933778289016427379, "bitstream_io", false, 9484912818224215141]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rav1e-8d8369a065764983\\dep-lib-rav1e", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}