{"rustc": 10895048813736897673, "features": "[\"bundle-translations\", \"codemap\", \"codemap-diagnostic\", \"default\", \"display-diagnostics\", \"fontdue\", \"image\", \"proc-macro2\", \"proc_macro_span\", \"quote\", \"rust\", \"software-renderer\"]", "declared_features": "[\"bundle-translations\", \"codemap\", \"codemap-diagnostic\", \"cpp\", \"default\", \"display-diagnostics\", \"fontdue\", \"image\", \"proc-macro2\", \"proc_macro_span\", \"quote\", \"rust\", \"sdf-fonts\", \"software-renderer\"]", "target": 5997077308893045496, "profile": 2225463790103693989, "path": 6121382189225255411, "deps": [[164398694462155020, "typed_index_collections", false, 1792420751846341347], [925920622245451495, "strum", false, 11101706732170471466], [1470679118034951355, "num_enum", false, 2858046726451342725], [1504670837852841510, "rowan", false, 2957169165140076174], [3046214465055776457, "fontdue", false, 10986055104498578331], [3060637413840920116, "proc_macro2", false, 12148754458356503610], [3150220818285335163, "url", false, 301212310175201752], [4264449604758607596, "resvg", false, 12236136062162122296], [5560038387766815647, "codemap", false, 18240095661529961352], [7379558455692600769, "by_address", false, 16451893457741589650], [7611461696084798871, "lyon_path", false, 6893661436771325597], [7721950305594023387, "codemap_diagnostic", false, 1495244532468670693], [7864482126583800221, "linked_hash_set", false, 490649219136958678], [8317047369197848296, "polib", false, 2804058986728961030], [9129745186230991347, "smol_str", false, 8085944834967134405], [10697383615564341592, "rayon", false, 1590776522101855885], [11293676373856528358, "derive_more", false, 16577986434672697943], [13011651213356413521, "lyon_extra", false, 16423487207133457126], [13028763805764736075, "image", false, 15621833269510184238], [14533926308594949856, "build_script_build", false, 10476887528189055058], [16326338539882746041, "itertools", false, 18365963528207662501], [16415649488169917901, "i_slint_common", false, 10069026558149343323], [17990358020177143287, "quote", false, 6660427873528748432]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\i-slint-compiler-3df5fdc1b143cd03\\dep-lib-i_slint_compiler", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}