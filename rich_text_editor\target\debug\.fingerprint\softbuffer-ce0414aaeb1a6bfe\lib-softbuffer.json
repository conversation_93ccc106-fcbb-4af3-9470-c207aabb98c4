{"rustc": 10895048813736897673, "features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"fastrand\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2241668132362809309, "path": 13984392342850178464, "deps": [[376837177317575824, "build_script_build", false, 8127374171640684250], [4143744114649553716, "raw_window_handle", false, 164830378543985072], [5986029879202738730, "log", false, 10051233085755496510], [10281541584571964250, "windows_sys", false, 18370242967013101443]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-ce0414aaeb1a6bfe\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}