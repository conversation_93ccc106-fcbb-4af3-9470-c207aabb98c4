{"rustc": 10895048813736897673, "features": "[\"accessibility\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-qt\", \"i-slint-backend-winit\", \"renderer-femtovg\", \"renderer-software\"]", "declared_features": "[\"accessibility\", \"backend-linuxkms\", \"backend-linuxkms-noseat\", \"backend-qt\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-linuxkms\", \"i-slint-backend-qt\", \"i-slint-backend-testing\", \"i-slint-backend-winit\", \"i-slint-renderer-skia\", \"raw-window-handle-06\", \"renderer-femtovg\", \"renderer-skia\", \"renderer-skia-opengl\", \"renderer-skia-vulkan\", \"renderer-software\", \"rtti\", \"system-testing\"]", "target": 17512649618174676683, "profile": 2241668132362809309, "path": 11279555278644908225, "deps": [[2134775856264564058, "i_slint_backend_qt", false, 15119779206761058250], [5756138707274668043, "i_slint_core_macros", false, 5855601678222436503], [7613193051436156431, "i_slint_core", false, 2271307309612874036], [7649432603155880922, "build_script_build", false, 4298292905912626126], [10411997081178400487, "cfg_if", false, 12090912622209515690], [17699788962609858224, "i_slint_backend_winit", false, 6562274650242857240]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\i-slint-backend-selector-b3faf0a37bbbd6a6\\dep-lib-i_slint_backend_selector", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}