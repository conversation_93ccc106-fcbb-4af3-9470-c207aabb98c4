{"rustc": 10895048813736897673, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 3000085285366345116, "deps": [[3060637413840920116, "proc_macro2", false, 4641457206596644822], [4899080583175475170, "semver", false, 15459766632609058880], [7392050791754369441, "ico", false, 1795197579498700900], [8008191657135824715, "thiserror", false, 8323388360524435803], [8292277814562636972, "tauri_utils", false, 14896737822610357838], [8319709847752024821, "uuid", false, 17380866397164780466], [9451456094439810778, "regex", false, 6394574719566070452], [9689903380558560274, "serde", false, 7115804616442323303], [9857275760291862238, "sha2", false, 12579346479970950359], [10301936376833819828, "json_patch", false, 8265435416034707497], [12687914511023397207, "png", false, 15536987110490249243], [14132538657330703225, "brotli", false, 9125180282967883960], [15367738274754116744, "serde_json", false, 5832692581797945321], [15622660310229662834, "walkdir", false, 4731812589605712605], [17990358020177143287, "quote", false, 10103186544792924985], [18066890886671768183, "base64", false, 3416009957788947113]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-d9bcaf2cf1fba02e\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}