{"rustc": 10895048813736897673, "features": "[\"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 17984201634715228204, "path": 16726254088674816740, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hashbrown-16e7d410b5ce74ba\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}