<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>内联测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        
        .chinese { color: red; font-weight: bold; }
        .english { color: blue; }
        .number { color: green; font-weight: bold; }
        .punctuation { color: purple; font-weight: bold; }
        
        .test-area {
            border: 2px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
            font-size: 18px;
        }
        
        button {
            padding: 10px 20px;
            font-size: 16px;
            margin: 5px;
            cursor: pointer;
            background: #007acc;
            color: white;
            border: none;
            border-radius: 4px;
        }
        
        textarea {
            width: 100%;
            height: 100px;
            font-size: 16px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>内联JavaScript测试</h1>
    
    <p>静态颜色测试：</p>
    <div class="test-area">
        <span class="chinese">这是红色汉字</span>
        <span class="english">This is blue English</span>
        <span class="number">123456</span>
        <span class="punctuation">！？，。</span>
    </div>
    
    <textarea id="input">Hello世界123！</textarea>
    
    <div>
        <button onclick="alert('JavaScript工作！')">弹窗测试</button>
        <button onclick="document.getElementById('output').innerHTML='<strong style=color:red>成功!</strong>'">DOM测试</button>
        <button onclick="processText()">颜色测试</button>
    </div>
    
    <div id="output" class="test-area">
        点击按钮测试...
    </div>
    
    <script>
        function processText() {
            var text = document.getElementById('input').value;
            var result = '';
            
            for (var i = 0; i < text.length; i++) {
                var char = text.charAt(i);
                var code = char.charCodeAt(0);
                var cls = '';
                
                if (code >= 0x4E00 && code <= 0x9FFF) {
                    cls = 'chinese';
                } else if ((code >= 65 && code <= 90) || (code >= 97 && code <= 122)) {
                    cls = 'english';
                } else if (code >= 48 && code <= 57) {
                    cls = 'number';
                } else if ('!@#$%^&*()_+-=[]{}|;:\'",.<>/?`~，。！？；：""''（）【】《》、…—'.indexOf(char) !== -1) {
                    cls = 'punctuation';
                } else {
                    cls = 'other';
                }
                
                if (char === ' ') {
                    result += '&nbsp;';
                } else {
                    result += '<span class="' + cls + '">' + char + '</span>';
                }
            }
            
            document.getElementById('output').innerHTML = result;
        }
    </script>
    
    <!-- 立即执行的脚本测试 -->
    <script>
        document.write('<p style="color: green; font-weight: bold;">✓ JavaScript可以执行（如果您看到这行绿色文字）</p>');
    </script>
    
    <noscript>
        <p style="color: red; font-weight: bold;">✗ JavaScript被禁用或无法执行</p>
    </noscript>
</body>
</html>
