use anyhow::{Result, anyhow};
use clipboard_win::{get_clipboard, set_clipboard};
use image::{ImageBuffer, Rgba};

/// 剪贴板内容类型
#[derive(Debug, Clone)]
pub enum ClipboardContent {
    Text(String),
    Image(ImageData),
}

/// 图片数据结构
#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct ImageData {
    pub data: Vec<u8>,
    pub width: u32,
    pub height: u32,
    pub format: ImageFormat,
}

/// 支持的图片格式
#[derive(Debug, Clone)]
pub enum ImageFormat {
    Rgba8,
    Rgb8,
    Png,
    Jpeg,
    Bmp,
}

/// 剪贴板处理器
#[derive(Clone)]
pub struct ClipboardHandler;

impl ClipboardHandler {
    pub fn new() -> Self {
        Self
    }

    /// 获取剪贴板内容
    pub fn get_clipboard_content(&self) -> Result<ClipboardContent> {
        // 优先尝试获取图片
        if self.has_image() {
            if let Ok(image_data) = self.get_clipboard_image() {
                return Ok(ClipboardContent::Image(image_data));
            }
        }

        // 然后尝试获取文本
        if let Ok(text) = self.get_clipboard_text() {
            return Ok(ClipboardContent::Text(text));
        }

        Err(anyhow!("剪贴板中没有支持的内容"))
    }

    /// 获取剪贴板中的文本
    pub fn get_clipboard_text(&self) -> Result<String> {
        match get_clipboard(clipboard_win::formats::Unicode) {
            Ok(text) => Ok(text),
            Err(e) => Err(anyhow!("无法获取剪贴板文本: {}", e)),
        }
    }

    /// 获取剪贴板中的图片
    pub fn get_clipboard_image(&self) -> Result<ImageData> {
        // 尝试获取位图数据
        match get_clipboard(clipboard_win::formats::Bitmap) {
            Ok(bitmap_data) => {
                // 简单的位图数据处理
                // 这里只是一个基本实现，实际的位图解析会更复杂
                Ok(ImageData {
                    data: bitmap_data,
                    width: 100, // 默认宽度
                    height: 100, // 默认高度
                    format: ImageFormat::Rgba8,
                })
            }
            Err(e) => Err(anyhow!("无法获取剪贴板图片: {}", e)),
        }
    }

    /// 设置剪贴板文本
    pub fn set_clipboard_text(&self, text: &str) -> Result<()> {
        match set_clipboard(clipboard_win::formats::Unicode, text) {
            Ok(_) => Ok(()),
            Err(e) => Err(anyhow!("无法设置剪贴板文本: {}", e)),
        }
    }

    /// 检查剪贴板是否包含文本
    pub fn has_text(&self) -> bool {
        clipboard_win::is_format_avail(clipboard_win::formats::Unicode.into())
    }

    /// 检查剪贴板是否包含图片
    pub fn has_image(&self) -> bool {
        clipboard_win::is_format_avail(clipboard_win::formats::Bitmap.into())
    }

    /// 获取剪贴板内容类型描述
    pub fn get_clipboard_info(&self) -> String {
        let mut info = Vec::new();

        if self.has_text() {
            info.push("文本");
        }

        if self.has_image() {
            info.push("图片");
        }

        if info.is_empty() {
            "无支持的内容".to_string()
        } else {
            format!("包含: {}", info.join(", "))
        }
    }
}



#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_clipboard_handler_creation() {
        let handler = ClipboardHandler::new();
        // 基本创建测试
        assert!(true);
    }

    #[test]
    fn test_clipboard_info() {
        let handler = ClipboardHandler::new();
        let info = handler.get_clipboard_info();
        // 信息应该是字符串
        assert!(!info.is_empty());
    }
}
