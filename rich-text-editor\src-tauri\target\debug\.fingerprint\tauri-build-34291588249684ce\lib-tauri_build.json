{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 16335202704479430798, "path": 551126641997132685, "deps": [[4450062412064442726, "dirs_next", false, 8067337743590998300], [4899080583175475170, "semver", false, 15459766632609058880], [7468248713591957673, "cargo_toml", false, 17017353018922383775], [8292277814562636972, "tauri_utils", false, 14896737822610357838], [9689903380558560274, "serde", false, 7115804616442323303], [10301936376833819828, "json_patch", false, 8265435416034707497], [13077543566650298139, "heck", false, 13064985892056666769], [13625485746686963219, "anyhow", false, 5618069634244328341], [14189313126492979171, "tauri_winres", false, 12810820539138978224], [15367738274754116744, "serde_json", false, 5832692581797945321], [15622660310229662834, "walkdir", false, 4731812589605712605]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-34291588249684ce\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}