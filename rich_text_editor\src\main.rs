use slint::{<PERSON>mponentHandle, VecModel, Timer, TimerMode};
use std::rc::Rc;
use std::fs;
use std::path::Path;
use anyhow::Result;

mod text_processor;
mod clipboard_handler;

use text_processor::TextProcessor;
use clipboard_handler::ClipboardHandler;

// 引入生成的Slint代码
slint::include_modules!();

// 自动保存文件路径
const AUTO_SAVE_FILE: &str = "auto_save.txt";

// 加载自动保存的内容
fn load_auto_save() -> String {
    if Path::new(AUTO_SAVE_FILE).exists() {
        fs::read_to_string(AUTO_SAVE_FILE).unwrap_or_default()
    } else {
        String::new()
    }
}

// 保存内容到自动保存文件
fn save_auto_save(content: &str) -> Result<()> {
    fs::write(AUTO_SAVE_FILE, content)?;
    Ok(())
}

fn main() -> Result<()> {
    // 创建主窗口
    let ui = MainWindow::new()?;

    // 创建文本处理器和剪贴板处理器
    let text_processor = TextProcessor::new();
    let clipboard_handler = ClipboardHandler::new();

    // 创建文本片段模型
    let text_segments = Rc::new(VecModel::<TextSegment>::default());

    // 设置初始数据
    ui.set_text_segments(text_segments.clone().into());

    // 加载自动保存的内容
    let saved_content = load_auto_save();
    ui.set_current_text(saved_content.clone().into());

    // 如果有保存的内容，立即处理显示
    if !saved_content.is_empty() {
        let segments = text_processor.process_text(&saved_content);
        text_segments.set_vec(vec![]);
        for segment in segments {
            text_segments.push(TextSegment {
                text: segment.text.into(),
                color: segment.color,
                font_size: 14.0,
            });
        }
        ui.set_status_text(format!("已加载自动保存内容 ({} 字符)", saved_content.chars().count()).into());
    }

    // 文本输入处理
    {
        let ui_weak = ui.as_weak();
        let text_segments = text_segments.clone();
        let text_processor = text_processor.clone();

        ui.on_text_changed(move |text| {
            let ui = ui_weak.unwrap();

            // 处理文本，生成带颜色的片段
            let segments = text_processor.process_text(&text);

            // 清空现有片段
            text_segments.set_vec(vec![]);

            // 添加新片段
            for segment in segments {
                text_segments.push(TextSegment {
                    text: segment.text.into(),
                    color: segment.color,
                    font_size: 14.0,
                });
            }

            ui.set_status_text(format!("字符数: {} | 自动保存已启用", text.chars().count()).into());
        });
    }

    // 自动保存定时器
    {
        let ui_weak = ui.as_weak();
        let timer = Timer::default();

        timer.start(TimerMode::Repeated, std::time::Duration::from_secs(3), move || {
            if let Some(ui) = ui_weak.upgrade() {
                let current_text = ui.get_current_text();
                if !current_text.is_empty() {
                    if let Err(e) = save_auto_save(&current_text) {
                        eprintln!("自动保存失败: {}", e);
                    }
                }
            }
        });
    }

    // 新建文件处理
    {
        let ui_weak = ui.as_weak();
        let text_segments = text_segments.clone();

        ui.on_new_file(move || {
            let ui = ui_weak.unwrap();
            ui.set_current_text("".into());
            text_segments.set_vec(vec![]);
            ui.set_status_text("新建文档".into());

            // 清空自动保存文件
            let _ = save_auto_save("");
        });
    }

    // 粘贴处理
    {
        let ui_weak = ui.as_weak();
        let clipboard_handler = clipboard_handler.clone();

        ui.on_paste_content(move || {
            let ui = ui_weak.unwrap();

            match clipboard_handler.get_clipboard_content() {
                Ok(content) => {
                    match content {
                        clipboard_handler::ClipboardContent::Text(text) => {
                            let current_text = ui.get_current_text();
                            let new_text = format!("{}{}", current_text, text);
                            ui.set_current_text(new_text.into());
                            ui.set_status_text("已粘贴文本".into());
                        }
                        clipboard_handler::ClipboardContent::Image(_image_data) => {
                            // 图片功能暂未实现
                            ui.set_status_text("图片功能暂未实现".into());
                        }
                    }
                }
                Err(e) => {
                    ui.set_status_text(format!("粘贴失败: {}", e).into());
                }
            }
        });
    }



    // 运行应用程序
    ui.run()?;

    Ok(())
}
