use slint::{<PERSON>mponentHandle, VecModel, Timer, TimerMode};
use std::rc::Rc;
use std::fs;
use std::path::Path;
use anyhow::Result;

mod text_processor;
mod clipboard_handler;

use text_processor::TextProcessor;
use clipboard_handler::ClipboardHandler;

// 引入生成的Slint代码
slint::include_modules!();

// 自动保存文件路径
const AUTO_SAVE_FILE: &str = "auto_save.txt";

// 加载自动保存的内容
fn load_auto_save() -> String {
    if Path::new(AUTO_SAVE_FILE).exists() {
        fs::read_to_string(AUTO_SAVE_FILE).unwrap_or_default()
    } else {
        String::new()
    }
}

// 保存内容到自动保存文件
fn save_auto_save(content: &str) -> Result<()> {
    fs::write(AUTO_SAVE_FILE, content)?;
    Ok(())
}

fn main() -> Result<()> {
    // 创建主窗口
    let ui = MainWindow::new()?;

    // 创建文本处理器和剪贴板处理器
    let text_processor = TextProcessor::new();
    let clipboard_handler = ClipboardHandler::new();

    // 创建文本片段模型
    let text_segments = Rc::new(VecModel::<TextSegment>::default());

    // 设置初始数据
    ui.set_text_segments(text_segments.clone().into());

    // 加载自动保存的内容
    let saved_content = load_auto_save();
    ui.set_current_text(saved_content.clone().into());

    // 如果有保存的内容，立即处理显示
    if !saved_content.is_empty() {
        let segments = text_processor.process_text(&saved_content);
        text_segments.set_vec(vec![]);
        for segment in segments {
            text_segments.push(TextSegment {
                text: segment.text.into(),
                color: segment.color,
                font_size: 14.0,
            });
        }
        ui.set_status_text(format!("已加载自动保存内容 ({} 字符)", saved_content.chars().count()).into());
    }

    // 文本输入处理
    {
        let ui_weak = ui.as_weak();
        let text_segments = text_segments.clone();
        let text_processor = text_processor.clone();

        ui.on_text_changed(move |text| {
            let ui = ui_weak.unwrap();

            // 处理文本，生成带颜色的片段（按行处理）
            let segments = text_processor.process_text(&text);

            // 清空现有片段
            text_segments.set_vec(vec![]);

            // 将所有片段合并成行，每行一个TextSegment
            let mut current_line = String::new();
            let mut line_segments = Vec::new();

            for segment in segments {
                if segment.text.contains('\n') {
                    // 处理包含换行的片段
                    let parts: Vec<&str> = segment.text.split('\n').collect();
                    for (i, part) in parts.iter().enumerate() {
                        if i > 0 {
                            // 完成当前行
                            if !current_line.is_empty() {
                                line_segments.push(current_line.clone());
                                current_line.clear();
                            }
                        }
                        current_line.push_str(part);
                    }
                } else {
                    current_line.push_str(&segment.text);
                }
            }

            // 添加最后一行
            if !current_line.is_empty() {
                line_segments.push(current_line);
            }

            // 为每行创建一个TextSegment，并重新着色
            for line in line_segments {
                if line.trim().is_empty() {
                    // 空行
                    text_segments.push(TextSegment {
                        text: " ".into(), // 空格占位
                        color: slint::Color::from_rgb_u8(200, 200, 200),
                        font_size: 14.0,
                    });
                } else {
                    // 重新处理这一行的颜色
                    let line_color_segments = text_processor.process_line(&line);
                    let mut colored_line = String::new();

                    for seg in line_color_segments {
                        colored_line.push_str(&seg.text);
                    }

                    // 使用第一个字符的颜色作为整行颜色（简化处理）
                    let first_char_color = if let Some(first_char) = line.chars().next() {
                        let char_type = text_processor.classify_character(&first_char.to_string());
                        text_processor.get_color_for_type(&char_type)
                    } else {
                        slint::Color::from_rgb_u8(0, 0, 0)
                    };

                    text_segments.push(TextSegment {
                        text: colored_line.into(),
                        color: first_char_color,
                        font_size: 14.0,
                    });
                }
            }

            // 分析文本中的字符类型并在状态栏显示
            let char_count = text.chars().count();
            let mut chinese_count = 0;
            let mut english_count = 0;
            let mut number_count = 0;
            let mut punct_count = 0;

            for c in text.chars() {
                if c >= '\u{4e00}' && c <= '\u{9fff}' {
                    chinese_count += 1;
                } else if c.is_ascii_alphabetic() {
                    english_count += 1;
                } else if c.is_ascii_digit() {
                    number_count += 1;
                } else if c.is_ascii_punctuation() || matches!(c, '，' | '。' | '！' | '？' | '；' | '：' | '"' | '"' | '\'' | '\'') {
                    punct_count += 1;
                }
            }

            let status = format!(
                "字符数: {} | 汉字: {} | 英文: {} | 数字: {} | 标点: {} | 自动保存已启用",
                char_count, chinese_count, english_count, number_count, punct_count
            );
            ui.set_status_text(status.into());
        });
    }

    // 自动保存定时器
    {
        let ui_weak = ui.as_weak();
        let timer = Timer::default();

        timer.start(TimerMode::Repeated, std::time::Duration::from_secs(3), move || {
            if let Some(ui) = ui_weak.upgrade() {
                let current_text = ui.get_current_text();
                if !current_text.is_empty() {
                    if let Err(e) = save_auto_save(&current_text) {
                        eprintln!("自动保存失败: {}", e);
                    }
                }
            }
        });
    }

    // 新建文件处理
    {
        let ui_weak = ui.as_weak();
        let text_segments = text_segments.clone();

        ui.on_new_file(move || {
            let ui = ui_weak.unwrap();
            ui.set_current_text("".into());
            text_segments.set_vec(vec![]);
            ui.set_status_text("新建文档".into());

            // 清空自动保存文件
            let _ = save_auto_save("");
        });
    }

    // 粘贴处理
    {
        let ui_weak = ui.as_weak();
        let clipboard_handler = clipboard_handler.clone();

        ui.on_paste_content(move || {
            let ui = ui_weak.unwrap();

            match clipboard_handler.get_clipboard_content() {
                Ok(content) => {
                    match content {
                        clipboard_handler::ClipboardContent::Text(text) => {
                            let current_text = ui.get_current_text();
                            let new_text = format!("{}{}", current_text, text);
                            ui.set_current_text(new_text.into());
                            ui.set_status_text("已粘贴文本".into());
                        }
                        clipboard_handler::ClipboardContent::Image(image_data) => {
                            // 图片粘贴：在文本中插入图片占位符
                            let current_text = ui.get_current_text();
                            let image_placeholder = format!("\n[图片: {}x{}]\n", image_data.width, image_data.height);
                            let new_text = format!("{}{}", current_text, image_placeholder);
                            ui.set_current_text(new_text.into());
                            ui.set_status_text("已粘贴图片（显示为占位符）".into());
                        }
                    }
                }
                Err(e) => {
                    ui.set_status_text(format!("粘贴失败: {}", e).into());
                }
            }
        });
    }



    // 运行应用程序
    ui.run()?;

    Ok(())
}
