{"rustc": 10895048813736897673, "features": "[\"accessibility\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-qt\", \"i-slint-backend-winit\", \"renderer-femtovg\", \"renderer-software\"]", "declared_features": "[\"accessibility\", \"backend-linuxkms\", \"backend-linuxkms-noseat\", \"backend-qt\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-linuxkms\", \"i-slint-backend-qt\", \"i-slint-backend-testing\", \"i-slint-backend-winit\", \"i-slint-renderer-skia\", \"raw-window-handle-06\", \"renderer-femtovg\", \"renderer-skia\", \"renderer-skia-opengl\", \"renderer-skia-vulkan\", \"renderer-software\", \"rtti\", \"system-testing\"]", "target": 17512649618174676683, "profile": 144525733998112618, "path": 11279555278644908225, "deps": [[2134775856264564058, "i_slint_backend_qt", false, 7145459514231873335], [5756138707274668043, "i_slint_core_macros", false, 8164074048816655906], [7613193051436156431, "i_slint_core", false, 14992940666072512665], [7649432603155880922, "build_script_build", false, 16029559625590710444], [10411997081178400487, "cfg_if", false, 5212407598375374865], [17699788962609858224, "i_slint_backend_winit", false, 2862570075582990843]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\i-slint-backend-selector-9682950da0b39457\\dep-lib-i_slint_backend_selector", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}