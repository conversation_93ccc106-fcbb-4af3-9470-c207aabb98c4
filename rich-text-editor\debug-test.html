<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        
        .chinese { color: red; font-weight: bold; }
        .english { color: blue; }
        .number { color: green; font-weight: bold; }
        .punctuation { color: purple; font-weight: bold; }
        
        .test-area {
            border: 2px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
            font-size: 18px;
        }
        
        button {
            padding: 10px 20px;
            font-size: 16px;
            margin: 5px;
            cursor: pointer;
            background: #007acc;
            color: white;
            border: none;
            border-radius: 4px;
        }
        
        button:hover {
            background: #005a9e;
        }
        
        textarea {
            width: 100%;
            height: 100px;
            font-size: 16px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .debug {
            background: #ffffcc;
            border: 1px solid #ffcc00;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>JavaScript调试测试</h1>
    
    <div class="debug">
        <strong>调试信息：</strong><br>
        <span id="loadStatus">页面加载中...</span>
    </div>
    
    <p>静态颜色测试（应该显示不同颜色）：</p>
    <div class="test-area">
        <span class="chinese">这是红色汉字</span>
        <span class="english">This is blue English</span>
        <span class="number">123456</span>
        <span class="punctuation">！？，。</span>
    </div>
    
    <textarea id="input">Hello世界123！测试文字</textarea>
    
    <div>
        <button id="btn1">测试按钮1</button>
        <button id="btn2">测试按钮2</button>
        <button id="btn3">颜色处理</button>
    </div>
    
    <div id="output" class="test-area">
        等待按钮点击...
    </div>
    
    <div class="debug">
        <strong>事件日志：</strong><br>
        <div id="eventLog">无事件</div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            console.log(message);
            var logDiv = document.getElementById('eventLog');
            logDiv.innerHTML += '<br>' + new Date().toLocaleTimeString() + ': ' + message;
        }
        
        // 页面加载事件
        document.addEventListener('DOMContentLoaded', function() {
            log('DOMContentLoaded事件触发');
            document.getElementById('loadStatus').innerHTML = 'DOM加载完成 ✓';
            
            // 绑定按钮事件
            document.getElementById('btn1').addEventListener('click', function() {
                log('按钮1被点击');
                document.getElementById('output').innerHTML = '<strong style="color: red;">按钮1点击成功！</strong>';
            });
            
            document.getElementById('btn2').addEventListener('click', function() {
                log('按钮2被点击');
                alert('按钮2点击成功！JavaScript工作正常！');
            });
            
            document.getElementById('btn3').addEventListener('click', function() {
                log('开始颜色处理');
                processColors();
            });
            
            log('所有事件监听器已绑定');
        });
        
        // 颜色处理函数
        function processColors() {
            try {
                var input = document.getElementById('input');
                var text = input.value;
                log('获取到文本: ' + text);
                
                var result = '';
                
                for (var i = 0; i < text.length; i++) {
                    var char = text.charAt(i);
                    var code = char.charCodeAt(0);
                    var className = '';
                    
                    // 字符分类
                    if (code >= 0x4E00 && code <= 0x9FFF) {
                        className = 'chinese';
                    } else if ((code >= 65 && code <= 90) || (code >= 97 && code <= 122)) {
                        className = 'english';
                    } else if (code >= 48 && code <= 57) {
                        className = 'number';
                    } else if ('!@#$%^&*()_+-=[]{}|;:\'",.<>/?`~，。！？；：""''（）【】《》、…—'.indexOf(char) !== -1) {
                        className = 'punctuation';
                    } else {
                        className = 'other';
                    }
                    
                    if (char === ' ') {
                        result += '&nbsp;';
                    } else {
                        result += '<span class="' + className + '">' + char + '</span>';
                    }
                }
                
                log('生成HTML长度: ' + result.length);
                document.getElementById('output').innerHTML = result;
                log('颜色处理完成');
                
            } catch (error) {
                log('错误: ' + error.message);
                alert('处理出错: ' + error.message);
            }
        }
        
        // 备用的window.onload
        window.onload = function() {
            log('window.onload事件触发');
            document.getElementById('loadStatus').innerHTML += ' | window.onload完成 ✓';
        };
        
        // 初始日志
        log('脚本开始执行');
    </script>
</body>
</html>
