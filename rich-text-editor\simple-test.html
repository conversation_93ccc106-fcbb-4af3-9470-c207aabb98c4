<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单颜色测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .input-area {
            width: 100%;
            height: 100px;
            border: 2px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-size: 16px;
            font-family: monospace;
            margin-bottom: 20px;
        }
        
        .output-area {
            min-height: 200px;
            border: 2px solid #007acc;
            border-radius: 4px;
            padding: 15px;
            font-size: 16px;
            font-family: monospace;
            line-height: 1.5;
            background: #fafafa;
        }
        
        /* 字符颜色样式 */
        .chinese {
            color: #dc143c;
            font-weight: bold;
        }
        
        .english {
            color: #1e90ff;
        }
        
        .number {
            color: #228b22;
            font-weight: bold;
        }
        
        .punctuation {
            color: #9400d3;
            font-weight: bold;
        }
        
        .whitespace {
            color: #808080;
        }
        
        .other {
            color: #ff8c00;
        }
        
        .btn {
            padding: 10px 20px;
            background: #007acc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #005a9e;
        }
        
        .info {
            background: #e9f7ff;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>富文本颜色测试 - 分离版本</h1>
        
        <div class="info">
            <strong>测试说明：</strong><br>
            在下方输入框中输入文字，点击"应用颜色"按钮，查看输出区域的颜色效果。<br>
            🔴 汉字=红色 | 🔵 英文=蓝色 | 🟢 数字=绿色 | 🟣 标点=紫色
        </div>
        
        <h3>输入区域：</h3>
        <textarea id="input" class="input-area" placeholder="请输入文字，例如：Hello世界123！">Hello世界123！这是测试文本。</textarea>
        
        <div>
            <button class="btn" onclick="applyColors()">应用颜色</button>
            <button class="btn" onclick="insertTest()">插入测试文本</button>
            <button class="btn" onclick="clearAll()">清空</button>
        </div>
        
        <h3>输出区域（带颜色）：</h3>
        <div id="output" class="output-area">
            点击"应用颜色"按钮查看效果...
        </div>
        
        <div id="debug" style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 4px; font-size: 12px;">
            调试信息将显示在这里...
        </div>
    </div>

    <script>
        function classifyCharacter(char) {
            const code = char.charCodeAt(0);
            
            console.log(`字符: "${char}", Unicode: ${code}`);
            
            // 汉字范围 (CJK统一汉字)
            if ((code >= 0x4E00 && code <= 0x9FFF) ||
                (code >= 0x3400 && code <= 0x4DBF)) {
                console.log(`  -> 汉字`);
                return 'chinese';
            }
            
            // 英文字母
            if ((code >= 65 && code <= 90) || (code >= 97 && code <= 122)) {
                console.log(`  -> 英文`);
                return 'english';
            }
            
            // 数字
            if (code >= 48 && code <= 57) {
                console.log(`  -> 数字`);
                return 'number';
            }
            
            // 标点符号
            const punctuationChars = '!@#$%^&*()_+-=[]{}\\|;:\'",.<>/?`~，。！？；：""''（）【】《》、…—';
            if (punctuationChars.includes(char)) {
                console.log(`  -> 标点`);
                return 'punctuation';
            }
            
            // 空白字符
            if (char === ' ' || char === '\t' || char === '\n' || char === '\r') {
                console.log(`  -> 空白`);
                return 'whitespace';
            }
            
            console.log(`  -> 其他`);
            return 'other';
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function addColors(text) {
            console.log('开始处理文本:', text);
            
            if (!text) return '';
            
            let result = '';
            let stats = {
                chinese: 0,
                english: 0,
                number: 0,
                punctuation: 0,
                whitespace: 0,
                other: 0
            };
            
            for (let i = 0; i < text.length; i++) {
                const char = text[i];
                const charType = classifyCharacter(char);
                stats[charType]++;
                
                if (char === '\n') {
                    result += '<br>';
                } else if (char === ' ') {
                    result += '<span class="whitespace">&nbsp;</span>';
                } else {
                    const escapedChar = escapeHtml(char);
                    const coloredChar = `<span class="${charType}">${escapedChar}</span>`;
                    result += coloredChar;
                    console.log(`字符 "${char}" -> ${coloredChar}`);
                }
            }
            
            console.log('处理结果:', result);
            console.log('统计:', stats);
            
            // 更新调试信息
            document.getElementById('debug').innerHTML = `
                <strong>调试信息：</strong><br>
                原文本长度: ${text.length}<br>
                汉字: ${stats.chinese} | 英文: ${stats.english} | 数字: ${stats.number} | 标点: ${stats.punctuation}<br>
                生成的HTML长度: ${result.length}<br>
                <details>
                    <summary>查看生成的HTML</summary>
                    <pre style="background: white; padding: 10px; margin-top: 10px; border-radius: 4px; overflow-x: auto;">${escapeHtml(result)}</pre>
                </details>
            `;
            
            return result;
        }
        
        function applyColors() {
            const input = document.getElementById('input');
            const output = document.getElementById('output');
            
            const text = input.value;
            console.log('输入文本:', text);
            
            if (!text.trim()) {
                output.innerHTML = '<em style="color: #999;">请输入一些文字...</em>';
                return;
            }
            
            const coloredHtml = addColors(text);
            output.innerHTML = coloredHtml;
            
            console.log('输出HTML已更新');
        }
        
        function insertTest() {
            const input = document.getElementById('input');
            input.value = 'Hello世界123！这是测试文本。English中文数字456，标点符号：；""''2025年4月完美实现！';
            applyColors();
        }
        
        function clearAll() {
            document.getElementById('input').value = '';
            document.getElementById('output').innerHTML = '点击"应用颜色"按钮查看效果...';
            document.getElementById('debug').innerHTML = '调试信息将显示在这里...';
        }
        
        // 页面加载完成后自动应用一次
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            applyColors();
        });
        
        // 输入时实时更新
        document.getElementById('input').addEventListener('input', function() {
            setTimeout(applyColors, 100);
        });
    </script>
</body>
</html>
