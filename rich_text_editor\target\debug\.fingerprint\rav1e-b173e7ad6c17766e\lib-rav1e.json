{"rustc": 10895048813736897673, "features": "[\"threading\"]", "declared_features": "[\"aom-sys\", \"asm\", \"av-metrics\", \"backtrace\", \"bench\", \"binaries\", \"byteorder\", \"capi\", \"cc\", \"channel-api\", \"check_asm\", \"clap\", \"clap_complete\", \"console\", \"crossbeam\", \"dav1d-sys\", \"decode_test\", \"decode_test_dav1d\", \"default\", \"desync_finder\", \"dump_ivf\", \"dump_lookahead_data\", \"fern\", \"git_version\", \"image\", \"ivf\", \"nasm-rs\", \"nom\", \"quick_test\", \"scan_fmt\", \"scenechange\", \"serde\", \"serde-big-array\", \"serialize\", \"signal-hook\", \"signal_support\", \"threading\", \"toml\", \"tracing\", \"tracing-chrome\", \"tracing-subscriber\", \"unstable\", \"wasm\", \"wasm-bindgen\", \"y4m\"]", "target": 12405811532001061035, "profile": 2225463790103693989, "path": 721753106137371345, "deps": [[1895578031732481377, "profiling", false, 7533719486078629742], [2687729594444538932, "debug_unreachable", false, 12873221711422519061], [2924422107542798392, "libc", false, 12478396635539682687], [3722963349756955755, "once_cell", false, 17902551282502804403], [5157631553186200874, "num_traits", false, 6395592101654586308], [5237962722597217121, "simd_helpers", false, 9313615990629926062], [5626665093607998638, "build_script_build", false, 5052947818534764303], [5986029879202738730, "log", false, 9373612845409647693], [7074416887430417773, "av1_grain", false, 17973534452330582175], [8008191657135824715, "thiserror", false, 3597972702070170668], [10411997081178400487, "cfg_if", false, 9886716789947666174], [11063920846464372013, "v_frame", false, 5817273277126423207], [11263754829263059703, "num_derive", false, 3394118514786267281], [12672448913558545127, "noop_proc_macro", false, 17615283072141879404], [13847662864258534762, "arrayvec", false, 11850956266658398664], [14931062873021150766, "itertools", false, 7101723946940588139], [16507960196461048755, "rayon", false, 170143180432419738], [17605717126308396068, "paste", false, 10575315512387353980], [17706129463675219700, "arg_enum_proc_macro", false, 614322028517303931], [17933778289016427379, "bitstream_io", false, 17556112699714468829]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rav1e-b173e7ad6c17766e\\dep-lib-rav1e", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}