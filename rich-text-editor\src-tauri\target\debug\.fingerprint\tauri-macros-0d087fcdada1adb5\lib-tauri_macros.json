{"rustc": 10895048813736897673, "features": "[\"compression\", \"shell-scope\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 3952363274013950517, "deps": [[2713742371683562785, "syn", false, 11975498422634590033], [3060637413840920116, "proc_macro2", false, 4641457206596644822], [8292277814562636972, "tauri_utils", false, 14896737822610357838], [13077543566650298139, "heck", false, 13064985892056666769], [17492769205600034078, "tauri_codegen", false, 10056475973837614692], [17990358020177143287, "quote", false, 10103186544792924985]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-0d087fcdada1adb5\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}