{"rustc": 10895048813736897673, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"iana-time-zone\", \"js-sys\", \"now\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 15315924755136109342, "profile": 2241668132362809309, "path": 10380762340853966562, "deps": [[1885655767270534569, "windows_link", false, 10864675339416328941], [5157631553186200874, "num_traits", false, 1731426428170050610]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\chrono-5dfd6ff8eaa28c39\\dep-lib-chrono", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}