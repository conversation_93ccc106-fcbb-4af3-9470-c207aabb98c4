<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>富文本记事本 - 演示版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            overflow: hidden;
        }

        .app {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 菜单栏 */
        .menu-bar {
            height: 40px;
            background: #ffffff;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            padding: 0 10px;
            gap: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .menu-btn {
            padding: 6px 12px;
            border: 1px solid #ccc;
            background: #f8f9fa;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
        }

        .menu-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .menu-btn:active {
            background: #dee2e6;
        }

        .menu-info {
            margin-left: 20px;
            color: #666;
            font-size: 12px;
        }

        /* 编辑器容器 */
        .editor-container {
            flex: 1;
            padding: 10px;
            background: #f5f5f5;
        }

        /* 富文本编辑器 */
        .rich-editor {
            width: 100%;
            height: 100%;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            font-size: 14px;
            line-height: 1.6;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            outline: none;
            overflow-y: auto;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            resize: none;
        }

        .rich-editor:focus {
            border-color: #007acc;
            box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
        }

        .rich-editor[placeholder]:empty:before {
            content: attr(placeholder);
            color: #999;
            font-style: italic;
        }

        /* 字符颜色样式 */
        .chinese {
            color: #dc143c; /* 深红色 - 汉字 */
            font-weight: 500;
        }

        .english {
            color: #1e90ff; /* 蓝色 - 英文 */
        }

        .number {
            color: #228b22; /* 绿色 - 数字 */
            font-weight: 600;
        }

        .punctuation {
            color: #9400d3; /* 紫色 - 标点符号 */
            font-weight: 500;
        }

        .whitespace {
            color: #808080; /* 灰色 - 空白字符 */
        }

        .other {
            color: #ff8c00; /* 橙色 - 其他字符 */
        }

        /* 状态栏 */
        .status-bar {
            height: 24px;
            background: #f0f0f0;
            border-top: 1px solid #ddd;
            display: flex;
            align-items: center;
            padding: 0 10px;
            font-size: 11px;
            color: #666;
        }

        /* 滚动条样式 */
        .rich-editor::-webkit-scrollbar {
            width: 8px;
        }

        .rich-editor::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .rich-editor::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .rich-editor::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 选中文本样式 */
        .rich-editor::selection {
            background: #b3d4fc;
        }
    </style>
</head>
<body>
    <div class="app">
        <!-- 菜单栏 -->
        <div class="menu-bar">
            <button id="newBtn" class="menu-btn">新建</button>
            <button id="pasteBtn" class="menu-btn">粘贴</button>
            <div class="menu-info">
                汉字红色 | English蓝色 | 123绿色 | 标点紫色
            </div>
        </div>

        <!-- 主编辑区域 -->
        <div class="editor-container">
            <div 
                id="editor" 
                class="rich-editor" 
                contenteditable="true" 
                placeholder="在此输入文本，支持富文本颜色显示..."
                spellcheck="false"
            ></div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <span id="statusText">就绪 - 请输入文本测试颜色效果</span>
        </div>
    </div>

    <script>
        class RichTextEditor {
            constructor() {
                this.editor = document.getElementById('editor');
                this.statusText = document.getElementById('statusText');
                this.newBtn = document.getElementById('newBtn');
                this.pasteBtn = document.getElementById('pasteBtn');
                
                this.isProcessing = false;
                
                this.init();
            }

            init() {
                // 绑定事件
                this.editor.addEventListener('input', this.handleInput.bind(this));
                this.editor.addEventListener('paste', this.handlePaste.bind(this));
                this.newBtn.addEventListener('click', this.handleNew.bind(this));
                this.pasteBtn.addEventListener('click', this.handlePasteButton.bind(this));
                
                // 初始化状态
                this.updateStatus();
            }

            // 处理输入事件
            handleInput() {
                if (this.isProcessing) return;
                
                // 延迟处理，避免频繁更新
                clearTimeout(this.inputTimer);
                this.inputTimer = setTimeout(() => {
                    this.processText();
                    this.updateStatus();
                }, 100);
            }

            // 处理文本，添加颜色
            processText() {
                this.isProcessing = true;
                
                // 保存光标位置
                const selection = window.getSelection();
                const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
                const cursorOffset = range ? range.startOffset : 0;
                const cursorContainer = range ? range.startContainer : null;
                
                // 获取纯文本
                const text = this.editor.textContent || '';
                
                // 处理文本并添加颜色
                const coloredHtml = this.addColors(text);
                
                // 更新HTML
                this.editor.innerHTML = coloredHtml;
                
                // 恢复光标位置
                this.restoreCursor(cursorContainer, cursorOffset);
                
                this.isProcessing = false;
            }

            // 为文本添加颜色
            addColors(text) {
                if (!text) return '';
                
                let result = '';
                
                for (let i = 0; i < text.length; i++) {
                    const char = text[i];
                    const charType = this.classifyCharacter(char);
                    
                    if (char === '\n') {
                        result += '<br>';
                    } else if (char === ' ') {
                        result += '<span class="whitespace">&nbsp;</span>';
                    } else {
                        result += `<span class="${charType}">${this.escapeHtml(char)}</span>`;
                    }
                }
                
                return result;
            }

            // 字符分类
            classifyCharacter(char) {
                const code = char.charCodeAt(0);
                
                // 汉字范围 (CJK统一汉字)
                if ((code >= 0x4E00 && code <= 0x9FFF) ||
                    (code >= 0x3400 && code <= 0x4DBF) ||
                    (code >= 0x20000 && code <= 0x2A6DF)) {
                    return 'chinese';
                }
                
                // 英文字母
                if ((code >= 65 && code <= 90) || (code >= 97 && code <= 122)) {
                    return 'english';
                }
                
                // 数字
                if (code >= 48 && code <= 57) {
                    return 'number';
                }
                
                // 空白字符
                if (char === ' ' || char === '\t' || char === '\n' || char === '\r') {
                    return 'whitespace';
                }
                
                // 标点符号
                if (this.isPunctuation(char)) {
                    return 'punctuation';
                }
                
                // 其他字符
                return 'other';
            }

            // 判断是否为标点符号
            isPunctuation(char) {
                const punctuationChars = '!@#$%^&*()_+-=[]{}\\|;:\'",.<>/?`~，。！？；：""''（）【】《》、…—';
                return punctuationChars.includes(char);
            }

            // HTML转义
            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            // 恢复光标位置
            restoreCursor(container, offset) {
                try {
                    const selection = window.getSelection();
                    const range = document.createRange();
                    
                    // 简化的光标恢复逻辑
                    if (this.editor.childNodes.length > 0) {
                        const lastNode = this.editor.childNodes[this.editor.childNodes.length - 1];
                        range.setStartAfter(lastNode);
                        range.collapse(true);
                        selection.removeAllRanges();
                        selection.addRange(range);
                    }
                } catch (e) {
                    // 光标恢复失败时，将光标放到末尾
                    this.editor.focus();
                }
            }

            // 更新状态栏
            updateStatus() {
                const text = this.editor.textContent || '';
                const stats = this.getTextStats(text);
                
                this.statusText.textContent = 
                    `字符数: ${stats.total} | 汉字: ${stats.chinese} | 英文: ${stats.english} | 数字: ${stats.number} | 标点: ${stats.punctuation} | 演示版本`;
            }

            // 获取文本统计
            getTextStats(text) {
                const stats = {
                    total: text.length,
                    chinese: 0,
                    english: 0,
                    number: 0,
                    punctuation: 0,
                    whitespace: 0,
                    other: 0
                };
                
                for (const char of text) {
                    const type = this.classifyCharacter(char);
                    if (stats.hasOwnProperty(type)) {
                        stats[type]++;
                    }
                }
                
                return stats;
            }

            // 处理粘贴事件
            handlePaste(e) {
                e.preventDefault();
                
                const clipboardData = e.clipboardData || window.clipboardData;
                const text = clipboardData.getData('text/plain');
                
                if (text) {
                    // 插入纯文本
                    document.execCommand('insertText', false, text);
                }
            }

            // 新建文档
            handleNew() {
                this.editor.innerHTML = '';
                this.editor.focus();
                this.updateStatus();
                this.statusText.textContent = '新建文档';
            }

            // 粘贴按钮
            async handlePasteButton() {
                try {
                    const text = await navigator.clipboard.readText();
                    if (text) {
                        document.execCommand('insertText', false, text);
                        this.statusText.textContent = '已粘贴文本';
                    } else {
                        this.statusText.textContent = '剪贴板中没有内容';
                    }
                } catch (error) {
                    this.statusText.textContent = '无法访问剪贴板，请使用Ctrl+V粘贴';
                }
            }
        }

        // 等待DOM加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new RichTextEditor();
        });
    </script>
</body>
</html>
