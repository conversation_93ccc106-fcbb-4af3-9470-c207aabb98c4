{"rustc": 10895048813736897673, "features": "[\"default\", \"png\", \"png-format\", \"simd\", \"std\"]", "declared_features": "[\"default\", \"no-std-float\", \"png\", \"png-format\", \"simd\", \"std\"]", "target": 5498047482726726170, "profile": 17984201634715228204, "path": 272324887422862316, "deps": [[5986029879202738730, "log", false, 6155596972104726174], [7880193374687061517, "tiny_skia_path", false, 6801424632926407706], [9529943735784919782, "arrayref", false, 13126565928662885397], [10411997081178400487, "cfg_if", false, 12398871947869950992], [12687914511023397207, "png", false, 9628421034420239437], [13847662864258534762, "arrayvec", false, 633736792643883911], [14074610438553418890, "bytemuck", false, 14857060863748685789]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tiny-skia-c65f7febc8543a2b\\dep-lib-tiny_skia", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}