{"rustc": 10895048813736897673, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"async\", \"default\", \"futures\", \"std\"]", "target": 8369499057004385739, "profile": 2241668132362809309, "path": 8129429860494916826, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\weezl-d32ad93df2bdf5f3\\dep-lib-weezl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}