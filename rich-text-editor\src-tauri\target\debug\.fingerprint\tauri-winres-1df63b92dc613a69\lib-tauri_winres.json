{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 10555948925697328552, "profile": 2225463790103693989, "path": 13628026510320951215, "deps": [[1293861355733423614, "toml", false, 15979022003313619787], [8955937299972520801, "embed_resource", false, 7666007012969738832]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winres-1df63b92dc613a69\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}