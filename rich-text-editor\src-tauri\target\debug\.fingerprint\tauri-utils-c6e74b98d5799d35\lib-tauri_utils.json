{"rustc": 10895048813736897673, "features": "[\"brotli\", \"compression\", \"glob\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 17109795825074435547, "deps": [[561782849581144631, "html5ever", false, 12855434348798339944], [3129130049864710036, "memchr", false, 5631815864646255704], [3150220818285335163, "url", false, 18397932542882334313], [3334271191048661305, "windows_version", false, 3503741764654053040], [4899080583175475170, "semver", false, 17641960539950484514], [5986029879202738730, "log", false, 9688976173557548651], [6213549728662707793, "serde_with", false, 6649733880071088623], [6262254372177975231, "kuchiki", false, 14085678581313275156], [6606131838865521726, "ctor", false, 12348064232428753234], [6997837210367702832, "infer", false, 3021076445743876748], [8008191657135824715, "thiserror", false, 8323388360524435803], [9689903380558560274, "serde", false, 7115804616442323303], [10301936376833819828, "json_patch", false, 102035114730421255], [11989259058781683633, "dunce", false, 2834297297971740684], [14132538657330703225, "brotli", false, 9125180282967883960], [15367738274754116744, "serde_json", false, 16057581619474505139], [15622660310229662834, "walkdir", false, 9497435952105006286], [17155886227862585100, "glob", false, 367035643737452713], [17186037756130803222, "phf", false, 15205437446286245418]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-c6e74b98d5799d35\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}