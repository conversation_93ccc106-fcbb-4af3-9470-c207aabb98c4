{"rustc": 10895048813736897673, "features": "[\"accessibility\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-qt\", \"i-slint-backend-winit\", \"renderer-femtovg\", \"renderer-software\"]", "declared_features": "[\"accessibility\", \"backend-linuxkms\", \"backend-linuxkms-noseat\", \"backend-qt\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-linuxkms\", \"i-slint-backend-qt\", \"i-slint-backend-testing\", \"i-slint-backend-winit\", \"i-slint-renderer-skia\", \"raw-window-handle-06\", \"renderer-femtovg\", \"renderer-skia\", \"renderer-skia-opengl\", \"renderer-skia-vulkan\", \"renderer-software\", \"rtti\", \"system-testing\"]", "target": 17512649618174676683, "profile": 15657897354478470176, "path": 11279555278644908225, "deps": [[2134775856264564058, "i_slint_backend_qt", false, 5244767426301918809], [5756138707274668043, "i_slint_core_macros", false, 5855601678222436503], [7613193051436156431, "i_slint_core", false, 13642983357584844417], [7649432603155880922, "build_script_build", false, 16093375381965391582], [10411997081178400487, "cfg_if", false, 9886716789947666174], [17699788962609858224, "i_slint_backend_winit", false, 8562256797375050905]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\i-slint-backend-selector-d76e362a34419c8f\\dep-lib-i_slint_backend_selector", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}