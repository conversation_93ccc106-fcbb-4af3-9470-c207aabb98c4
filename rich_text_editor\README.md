# 富文本记事本 - Rust + Slint

这是一个使用Rust语言和Slint GUI框架开发的富文本记事本，采用传统记事本布局，支持中英文数字标点符号的不同颜色显示和自动保存功能。

## 功能特性

### ✅ 已实现功能
- **富文本颜色显示**：
  - 🔴 汉字显示为红色
  - 🔵 English显示为蓝色
  - 🟢 123数字显示为绿色
  - 🟣 标点符号显示为紫色
  - ⚪ 空白字符显示为灰色
  - 🟠 其他字符显示为橙色

- **基本编辑功能**：
  - 全屏文本编辑器，类似传统记事本
  - 文本输入和实时颜色渲染
  - 剪贴板文本粘贴支持
  - 新建文档功能

- **自动保存功能**：
  - 每3秒自动保存当前内容
  - 程序重启时自动加载上次的内容
  - 无需手动保存，永不丢失数据

### 🚧 待实现功能
- 图片粘贴和显示功能
- 文件打开和保存对话框
- 更多编辑功能（撤销/重做、查找替换等）
- 字体大小和样式调整

## 使用方法

### 编译运行
```bash
# 进入项目目录
cd rich_text_editor

# 编译并运行
cargo run
```

### 操作说明
1. **文本编辑**：直接在主编辑区域输入文本，支持多行编辑
2. **颜色显示**：文本会根据字符类型自动显示不同颜色
3. **粘贴功能**：点击"粘贴"按钮或使用Ctrl+V粘贴剪贴板内容
4. **新建文档**：点击"新建"按钮清空当前内容
5. **自动保存**：程序会自动保存，下次打开时恢复上次的内容

### 测试示例
尝试输入以下文本来测试颜色显示效果：
```
Hello世界123！这是一个测试文本。
English中文数字456，标点符号：；""''
```

## 技术架构

### 项目结构
```
rich_text_editor/
├── src/
│   ├── main.rs              # 主程序入口
│   ├── text_processor.rs    # 文本处理和字符分类
│   └── clipboard_handler.rs # 剪贴板操作
├── ui/
│   └── main.slint          # Slint UI界面定义
├── build.rs                # 构建脚本
└── Cargo.toml             # 项目配置
```

### 核心模块
- **TextProcessor**：负责文本字符分类和颜色分配
- **ClipboardHandler**：处理Windows系统剪贴板操作
- **AutoSave**：自动保存机制，每3秒保存一次
- **Slint UI**：简洁的记事本风格界面

### 字符分类算法
程序使用Unicode字符范围来准确识别不同类型的字符：
- 汉字：CJK统一汉字区块（U+4E00-U+9FFF等）
- 英文：ASCII字母（a-z, A-Z）
- 数字：ASCII数字（0-9）
- 标点：常见英文和中文标点符号

## 开发环境

### 系统要求
- Windows 10/11
- Rust 1.70+
- Visual Studio Build Tools（用于编译Windows依赖）

### 依赖包
- `slint = "1.8"`：GUI框架
- `unicode-segmentation = "1.12"`：Unicode文本分割
- `clipboard-win = "5.4"`：Windows剪贴板操作
- `anyhow = "1.0"`：错误处理

## 编译优化

### 生成小体积exe文件
在`Cargo.toml`中添加以下配置：
```toml
[profile.release]
opt-level = "z"     # 优化文件大小
lto = true          # 链接时优化
codegen-units = 1   # 减少代码生成单元
panic = "abort"     # 减少panic处理代码
strip = true        # 移除调试符号
```

然后使用release模式编译：
```bash
cargo build --release
```

## 未来计划

1. **图片支持**：完善图片粘贴和显示功能
2. **文件操作**：实现完整的文件打开/保存对话框
3. **编辑增强**：添加撤销/重做、查找替换等功能
4. **界面优化**：改进用户体验和视觉效果
5. **性能优化**：处理大文件时的性能优化

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
