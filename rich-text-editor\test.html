<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>富文本颜色测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .editor {
            width: 100%;
            min-height: 300px;
            border: 2px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            font-size: 16px;
            line-height: 1.5;
            font-family: 'Consolas', 'Monaco', monospace;
            outline: none;
            background: white;
        }
        
        .editor:focus {
            border-color: #007acc;
        }
        
        /* 字符颜色样式 */
        .chinese {
            color: #dc143c !important; /* 深红色 - 汉字 */
            font-weight: bold;
        }
        
        .english {
            color: #1e90ff !important; /* 蓝色 - 英文 */
        }
        
        .number {
            color: #228b22 !important; /* 绿色 - 数字 */
            font-weight: bold;
        }
        
        .punctuation {
            color: #9400d3 !important; /* 紫色 - 标点符号 */
            font-weight: bold;
        }
        
        .whitespace {
            color: #808080 !important; /* 灰色 - 空白字符 */
        }
        
        .other {
            color: #ff8c00 !important; /* 橙色 - 其他字符 */
        }
        
        .info {
            margin-top: 10px;
            padding: 10px;
            background: #e9f7ff;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }
        
        .test-btn {
            margin-top: 10px;
            padding: 8px 16px;
            background: #007acc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .test-btn:hover {
            background: #005a9e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>富文本颜色显示测试</h1>
        
        <div class="info">
            <strong>颜色规则：</strong><br>
            🔴 汉字 = 红色 | 🔵 英文 = 蓝色 | 🟢 数字 = 绿色 | 🟣 标点 = 紫色
        </div>
        
        <div 
            id="editor" 
            class="editor" 
            contenteditable="true" 
            placeholder="请在此输入文本测试颜色效果..."
        >请输入文字测试：Hello世界123！</div>
        
        <button class="test-btn" onclick="insertTestText()">插入测试文本</button>
        <button class="test-btn" onclick="clearEditor()">清空</button>
        
        <div id="status" class="status">
            状态：等待输入...
        </div>
    </div>

    <script>
        let isProcessing = false;
        
        function classifyCharacter(char) {
            const code = char.charCodeAt(0);
            
            // 汉字范围 (CJK统一汉字)
            if ((code >= 0x4E00 && code <= 0x9FFF) ||
                (code >= 0x3400 && code <= 0x4DBF)) {
                return 'chinese';
            }
            
            // 英文字母
            if ((code >= 65 && code <= 90) || (code >= 97 && code <= 122)) {
                return 'english';
            }
            
            // 数字
            if (code >= 48 && code <= 57) {
                return 'number';
            }
            
            // 标点符号
            const punctuationChars = '!@#$%^&*()_+-=[]{}\\|;:\'",.<>/?`~，。！？；：""''（）【】《》、…—';
            if (punctuationChars.includes(char)) {
                return 'punctuation';
            }
            
            // 空白字符
            if (char === ' ' || char === '\t' || char === '\n' || char === '\r') {
                return 'whitespace';
            }
            
            // 其他字符
            return 'other';
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function addColors(text) {
            if (!text) return '';
            
            let result = '';
            
            for (let i = 0; i < text.length; i++) {
                const char = text[i];
                const charType = classifyCharacter(char);
                
                if (char === '\n') {
                    result += '<br>';
                } else if (char === ' ') {
                    result += '<span class="whitespace">&nbsp;</span>';
                } else {
                    result += `<span class="${charType}">${escapeHtml(char)}</span>`;
                }
            }
            
            return result;
        }
        
        function saveCaretPosition(element) {
            const selection = window.getSelection();
            if (selection.rangeCount === 0) return null;
            
            const range = selection.getRangeAt(0);
            const preCaretRange = range.cloneRange();
            preCaretRange.selectNodeContents(element);
            preCaretRange.setEnd(range.endContainer, range.endOffset);
            const caretOffset = preCaretRange.toString().length;
            
            return caretOffset;
        }
        
        function restoreCaretPosition(element, caretOffset) {
            if (caretOffset === null) return;
            
            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );
            
            let currentOffset = 0;
            let node;
            
            while (node = walker.nextNode()) {
                const nodeLength = node.textContent.length;
                if (currentOffset + nodeLength >= caretOffset) {
                    const range = document.createRange();
                    const selection = window.getSelection();
                    
                    range.setStart(node, caretOffset - currentOffset);
                    range.collapse(true);
                    
                    selection.removeAllRanges();
                    selection.addRange(range);
                    return;
                }
                currentOffset += nodeLength;
            }
            
            // 如果没找到合适位置，放到末尾
            const range = document.createRange();
            const selection = window.getSelection();
            range.selectNodeContents(element);
            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);
        }
        
        function processText() {
            if (isProcessing) return;
            isProcessing = true;
            
            const editor = document.getElementById('editor');
            const caretOffset = saveCaretPosition(editor);
            
            // 获取纯文本
            const text = editor.textContent || '';
            
            // 处理文本并添加颜色
            const coloredHtml = addColors(text);
            
            // 更新HTML
            editor.innerHTML = coloredHtml;
            
            // 恢复光标位置
            restoreCaretPosition(editor, caretOffset);
            
            // 更新状态
            updateStatus(text);
            
            isProcessing = false;
        }
        
        function updateStatus(text) {
            const stats = {
                total: text.length,
                chinese: 0,
                english: 0,
                number: 0,
                punctuation: 0,
                other: 0
            };
            
            for (const char of text) {
                const type = classifyCharacter(char);
                if (stats.hasOwnProperty(type)) {
                    stats[type]++;
                }
            }
            
            document.getElementById('status').textContent = 
                `字符数: ${stats.total} | 汉字: ${stats.chinese} | 英文: ${stats.english} | 数字: ${stats.number} | 标点: ${stats.punctuation}`;
        }
        
        function insertTestText() {
            const editor = document.getElementById('editor');
            const testText = 'Hello世界123！这是测试文本。English中文数字456，标点符号：；""''2025年4月完美实现！';
            editor.textContent = testText;
            processText();
        }
        
        function clearEditor() {
            const editor = document.getElementById('editor');
            editor.innerHTML = '';
            editor.focus();
            updateStatus('');
        }
        
        // 绑定事件
        document.addEventListener('DOMContentLoaded', function() {
            const editor = document.getElementById('editor');
            
            editor.addEventListener('input', function() {
                setTimeout(processText, 10);
            });
            
            editor.addEventListener('paste', function(e) {
                e.preventDefault();
                const text = (e.clipboardData || window.clipboardData).getData('text/plain');
                document.execCommand('insertText', false, text);
            });
            
            // 初始处理
            processText();
        });
    </script>
</body>
</html>
