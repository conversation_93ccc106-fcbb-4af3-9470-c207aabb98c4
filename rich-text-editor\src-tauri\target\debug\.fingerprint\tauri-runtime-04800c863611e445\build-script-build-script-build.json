{"rustc": 10895048813736897673, "features": "[\"clipboard\"]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 5408242616063297496, "profile": 16335202704479430798, "path": 6532763057939608250, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-04800c863611e445\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}