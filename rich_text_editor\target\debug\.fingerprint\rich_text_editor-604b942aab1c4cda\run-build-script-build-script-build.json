{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4792644333863610003, "build_script_build", false, 7455371838218695962]], "local": [{"RerunIfChanged": {"output": "debug\\build\\rich_text_editor-604b942aab1c4cda\\output", "paths": ["target\\debug\\build\\SLINT_DEFAULT_STYLE.txt", "ui/main.slint"]}}, {"RerunIfEnvChanged": {"var": "SLINT_STYLE", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_FONT_SIZES", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_SCALE_FACTOR", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_ASSET_SECTION", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_EMBED_RESOURCES", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_EMIT_DEBUG_INFO", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}