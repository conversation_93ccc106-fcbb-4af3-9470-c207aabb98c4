{"rustc": 10895048813736897673, "features": "[\"egl\", \"glutin_egl_sys\", \"glutin_glx_sys\", \"glutin_wgl_sys\", \"glx\", \"libloading\", \"wayland\", \"wayland-sys\", \"wgl\", \"windows-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"egl\", \"glutin_egl_sys\", \"glutin_glx_sys\", \"glutin_wgl_sys\", \"glx\", \"libloading\", \"wayland\", \"wayland-sys\", \"wgl\", \"windows-sys\", \"x11\", \"x11-dl\"]", "target": 1600304521922079521, "profile": 144525733998112618, "path": 9156147889705076316, "deps": [[3722963349756955755, "once_cell", false, 6561813677248844886], [4143744114649553716, "raw_window_handle", false, 11296632524748108325], [6763978947554154845, "windows_sys", false, 194499320878133638], [7191709312698686449, "glutin_egl_sys", false, 9972011209495878934], [7478509218540386589, "glutin_wgl_sys", false, 5547054690235349387], [7896293946984509699, "bitflags", false, 13148998852031461988], [10058659651543567831, "build_script_build", false, 2491001023212261399], [13587469111750606423, "libloading", false, 7088497023160261655]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\glutin-b464cfdb8326ca33\\dep-lib-glutin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}